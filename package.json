{"name": "vnsky-api", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node src/app.js", "worker:assign": "node src/workers/assignPhoneWorker.js", "worker:upload": "node src/workers/uploadPhoneWorker.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@fastify/multipart": "^9.0.3", "@prisma/client": "^6.9.0", "bullmq": "^5.53.2", "dotenv": "^16.5.0", "fastify": "^5.3.3", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jwt-decode": "^4.0.0", "node-cron": "^4.1.0", "puppeteer": "^24.10.0", "puppeteer-extra": "^3.3.6", "uuid": "^11.1.0", "winston": "^3.17.0"}}