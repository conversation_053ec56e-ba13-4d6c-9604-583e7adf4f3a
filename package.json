{"name": "vnsky-api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@fastify/multipart": "^9.0.3", "@prisma/client": "^6.9.0", "bullmq": "^5.53.2", "dotenv": "^16.5.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "puppeteer": "^24.10.0", "puppeteer-extra": "^3.3.6", "uuid": "^11.1.0", "winston": "^3.17.0"}}