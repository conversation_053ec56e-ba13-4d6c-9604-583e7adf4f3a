const { prisma } = require('../shared/instances');

/**
 * Authentication middleware for UID-based authentication
 * Used for: /api/assign, /api/upload, /api/scan
 * Expects: x-api-key header containing uid
 */
const authenticateWithUID = async (request, reply) => {
    const uid = request.headers['x-api-key'];
    
    if (!uid) {
        return reply.code(401).send({
            code: 401,
            message: 'Missing x-api-key header'
        });
    }

    try {
        // Find user by uid and ensure they are active
        const user = await prisma.user.findFirst({
            where: {
                uid: uid,
                status: 'active'
            },
            include: {
                storage: true
            }
        });

        if (!user) {
            return reply.code(401).send({
                code: 401,
                message: 'Invalid or inactive user'
            });
        }

        // Add user and storage info to request for use in route handlers
        request.user = user;
        request.storage = user.storage;
        
    } catch (error) {
        return reply.code(500).send({
            code: 500,
            message: 'Authentication error'
        });
    }
};

/**
 * Authentication middleware for API key-based authentication
 * Used for: /api/update, /api/user
 * Expects: x-api-key header containing storage code (since Storage model doesn't have apikey field)
 */
const authenticateWithAPIKey = async (request, reply) => {
    const apikey = request.headers['x-api-key'];

    if (!apikey) {
        return reply.code(401).send({
            code: 401,
            message: 'Missing x-api-key header'
        });
    }

    try {
        // Find storage by code (using code as API key since no apikey field exists)
        const storage = await prisma.storage.findFirst({
            where: {
                code: apikey,
                status: 'active'
            }
        });

        if (!storage) {
            return reply.code(401).send({
                code: 401,
                message: 'Invalid API key or inactive storage'
            });
        }

        // Add storage info to request for use in route handlers
        request.storage = storage;

    } catch (error) {
        return reply.code(500).send({
            code: 500,
            message: 'Authentication error'
        });
    }
};

module.exports = {
    authenticateWithUID,
    authenticateWithAPIKey
};
