const { fastify, prisma, redis } = require('./shared/instances');
const fastifyMultipart = require('@fastify/multipart');
const cron = require('node-cron');
const config = require('./config');
const { updateProductOrders } = require('./services/productOrderUpdater');
const PuppeteerHelper = require('./services/puppeteerHelper');

// Register plugins
fastify.register(fastifyMultipart, { limits: { fileSize: 5 * 1024 * 1024 } }); // 5MB limit
fastify.register(require('./api/user'), { prefix: '/api' });
fastify.register(require('./api/auth'), { prefix: '/api' });
fastify.register(require('./api/assign'), { prefix: '/api' });
fastify.register(require('./api/upload'), { prefix: '/api' });
fastify.register(require('./api/scan'), { prefix: '/api' });
fastify.register(require('./api/update'), { prefix: '/api' });
fastify.register(require('./api/cleanup'), { prefix: '/api' });
fastify.register(require('./api/assignLog'), { prefix: '/api' });

// Schedule product order update at midnight daily
cron.schedule('0 0 * * *', async () => {
    fastify.log.info('Starting daily product order update');
    try {
        await updateProductOrders(prisma, redis, fastify.log);
        fastify.log.info('Product order update completed');
    } catch (error) {
        fastify.log.error('Product order update failed:', error.message);
    }
}, {
    timezone: 'Asia/Ho_Chi_Minh' // Set to Vietnam timezone
});

// Graceful shutdown
const shutdown = async () => {
    fastify.log.info('Shutting down server...');
    await fastify.close();
    await prisma.$disconnect();
    await redis.quit();
    await PuppeteerHelper.closeAll();
    process.exit(0);
};

process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);
// Start server
const start = async () => {
    try {
        fastify.listen({ port: config.port, host: '0.0.0.0' });
        fastify.log.info(`Server running on http://0.0.0.0:${config.port}`);
    } catch (err) {
        fastify.log.error(err);
        process.exit(1);
    }
};

start();