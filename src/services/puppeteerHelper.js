const puppeteer = require('puppeteer');
const { jwtDecode } = require('jwt-decode');
const { redis } = require('../shared/instances');
function getFormattedDate(date) {
    let day = date.getDate();
    let month = date.getMonth() + 1; // getMonth() is zero-based
    let year = date.getFullYear();

    // Pad with a leading zero if the day or month is a single digit
    day = day < 10 ? '0' + day : day;
    month = month < 10 ? '0' + month : month;

    return `${day}/${month}/${year}`;
}
class PuppeteerHelper {
    static instances = new Map();

    constructor(username, password, otp) {
        this.username = username;
        this.password = password;
        this.otp = otp;
        this.baseUrl = 'https://websale.vnsky.vn';
        this.browser = null;
        this.page = null;
        this.token = null;
        this.tokenExpiry = null;
        this.maxRetries = 3;
        this.evaluateTimeout = 30000; // 30 seconds
    }

    static getInstance(username, password, otp) {
        if (!this.instances.has(username)) {
            const instance = new PuppeteerHelper(username, password, otp);
            this.instances.set(username, instance);
        }
        return this.instances.get(username);
    }

    static async closeAll() {
        for (const instance of this.instances.values()) {
            await instance.close();
        }
        this.instances.clear();
    }

    async init() {
        if (!this.browser || !this.browser.connected) {
            this.browser = await puppeteer.launch({ headless: true });
            await this.createPage();
        }
    }

    async createPage() {
        if (this.page && !this.page.isClosed()) {
            await this.page.close().catch(() => { });
        }
        this.page = await this.browser.newPage();
        await this.page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
    }

    async fetch_request(url, options = {}, retries = this.maxRetries) {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                if (!this.page || this.page.isClosed() || !this.browser.isConnected()) {
                    await this.createPage();
                }

                const timeoutPromise = new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('page.evaluate timed out')), this.evaluateTimeout)
                );

                const result = await Promise.race([
                    this.page.evaluate(async (url, options) => {
                        try {
                            const response = await fetch(url, {
                                ...options,
                                headers: {
                                    'Accept': 'application/json, text/plain, */*',
                                    'Content-Type': 'application/json',
                                    'Accept-Language': 'en,fr;q=0.9,vi;q=0.8',
                                    'Origin': 'https://websale.vnsky.vn',
                                    'Referer': 'https://websale.vnsky.vn/websale/pages/authentication/login-v2?returnUrl=%2Fdashboard',
                                    'Sec-Fetch-Dest': 'empty',
                                    'Sec-Fetch-Mode': 'cors',
                                    'Sec-Fetch-Site': 'same-origin',
                                    ...options.headers
                                }
                            });
                            const data = await response.json();
                            return {
                                status: response.status,
                                headers: Object.fromEntries(response.headers.entries()),
                                data
                            };
                        } catch (error) {
                            throw new Error(`Fetch request failed: ${error.message}`);
                        }
                    }, url, options),
                    timeoutPromise
                ]);

                if (result.status === 504) {
                    throw new Error('504 Gateway Timeout');
                }

                return result;
            } catch (error) {
                console.warn(`Attempt ${attempt} failed for ${url}: ${error.message}`);
                if (error.message.includes('Execution context was destroyed') || error.message.includes('Target closed')) {
                    await this.createPage();
                }
                if (attempt === retries) {
                    throw new Error(`Fetch request failed after ${retries} attempts: ${error.message}`);
                }
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }

    async loadToken() {
        const tokenData = await redis.get(`puppeteer_token:${this.username}`);
        if (tokenData) {
            const { token, tokenExpiry } = JSON.parse(tokenData);
            const now = Date.now();
            const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
            if (tokenExpiry - now > bufferTime) {
                this.token = token;
                this.tokenExpiry = tokenExpiry;
                return true;
            }
        }
        return false;
    }

    async saveToken() {
        if (this.token && this.tokenExpiry) {
            await redis.set(
                `puppeteer_token:${this.username}`,
                JSON.stringify({ token: this.token, tokenExpiry: this.tokenExpiry }),
                'EX',
                Math.floor((this.tokenExpiry - Date.now()) / 1000)
            );
        }
    }

    async login() {
        try {
            if (await this.loadToken()) {
                return;
            }

            await this.init();

            const loginResponse = await this.fetch_request(`${this.baseUrl}/websale-core-service/login`, {
                method: 'POST',
                body: JSON.stringify({
                    username: this.username,
                    password: this.password
                })
            });

            if (loginResponse.status !== 200 || loginResponse.data.statusCode !== 200 || loginResponse.data.code !== 0) {
                throw new Error('Login failed: Invalid credentials');
            }

            const otpResponse = await this.fetch_request(`${this.baseUrl}/websale-core-service/user-info/getotp`, {
                method: 'POST',
                body: JSON.stringify({
                    username: this.username,
                    otp: this.otp
                })
            });

            if (otpResponse.status !== 200 || otpResponse.data.statusCode !== 200 || otpResponse.data.code !== 0) {
                throw new Error('OTP verification failed: Invalid OTP');
            }

            const token = otpResponse.headers['authorization'];
            if (!token || !token.startsWith('Bearer ')) {
                throw new Error('No valid token received');
            }

            this.token = token.replace('Bearer ', '');
            const decoded = jwtDecode(this.token);
            this.tokenExpiry = decoded.exp * 1000;
            await this.saveToken();
        } catch (error) {
            throw new Error(`Login failed: ${error.message}`);
        }
    }

    async ensure_authenticated() {
        await this.init();
        const now = Date.now();
        const bufferTime = 5 * 60 * 1000; // 5 minutes buffer

        if (!this.token || !this.tokenExpiry || this.tokenExpiry - now < bufferTime) {
            await this.login();
        }
    }

    async get_product_orders() {
        await this.ensure_authenticated();
        const response = await this.fetch_request(`${this.baseUrl}/websale-core-service/order/npp-po?poName=&from=01/01/2024&to=${getFormattedDate(new Date())}&status=`, {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Referer': 'https://websale.vnsky.vn/websale/supplier/po'
            }
        });

        if (response.status !== 200 || response.data.statusCode !== 200 || response.data.code !== 0) {
            throw new Error('Failed to fetch product orders');
        }

        return response.data.content;
    }

    async get_product_order_detail(orderId) {
        await this.ensure_authenticated();
        const response = await this.fetch_request(`${this.baseUrl}/websale-core-service/order/${orderId}?phoneNumber=&status=`, {
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Referer': 'https://websale.vnsky.vn/websale/supplier/po'
            }
        });

        if (response.status !== 200 || response.data.statusCode !== 200 || response.data.code !== 0) {
            throw new Error('Failed to fetch product order detail');
        }

        return response.data.content;
    }

    async assign_phone(phoneNumber, tariffId, orderDetailId, orderInfoId) {
        await this.ensure_authenticated();
        const response = await this.fetch_request(`${this.baseUrl}/websale-core-service/user-info/npp-added-id`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`,
                'Referer': 'https://websale.vnsky.vn/websale/supplier/assign-package'
            },
            body: JSON.stringify({
                phoneNumber,
                tariffId,
                orderDetailId,
                orderInfoId
            })
        });

        if (response.status !== 200 || response.data.statusCode !== 200 || response.data.code !== 0) {
            throw new Error(`Failed to assign phone: ${response.data.errorMessages || 'Unknown error'}`);
        }

        return response.data;
    }

    async close() {
        if (this.browser && this.browser.isConnected()) {
            await this.browser.close().catch(() => { });
            this.browser = null;
            this.page = null;
        }
    }
}

module.exports = PuppeteerHelper;