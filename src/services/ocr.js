const { GoogleGenerativeAI } = require("@google/generative-ai");

const googleGenerativeAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

const model = googleGenerativeAI.getGenerativeModel({
    model: "models/gemini-2.0-flash-exp",
});

function getMimeType(base64String) {
    if (base64String.startsWith("iVBORw0KGgo=")) {
        return "image/png";
    } else if (base64String.startsWith("/9j/")) {
        return "image/jpeg";
    } else if (base64String.startsWith("R0lGODdh")) {
        return "image/gif";
    } else if (base64String.startsWith("UklGR")) {
        return "image/webp";
    } else {
        return null; // Return null for unknown mime type
    }
}

async function generateContent(prompt) {
    try {
        const result = await model.generateContent(prompt, { timeout: 60000 });
        const text = result.response.text()?.toUpperCase()?.replace(/<TAB>/g, " ");
        const arr = text.split('\n').reduce((acc, line) => {
            const [phone_number, serial] = line.split('\t');
            if (phone_number && serial) {
                acc.push({ phone_number, serial });
            }
            return acc;
        }, []);
        return arr;
    } catch (error) {
        console.log(error.message);
        return [];
    }
}
async function gemini_ocr(buffers) {
    const base64Photos = buffers.map(b => b.toString('base64'));
    const mappingPhotos = base64Photos.map((base64) => {
        const mime = getMimeType(base64);
        if (!mime) {
            return null;
        }
        return {
            inlineData: {
                data: base64,
                mimeType: mime,
            },
        };
    }).filter(Boolean);
    const prompt = [
        `get info of "Số thuê bao" and "Serial" of each sim card in each photo and return data with format: SO_THUE_BAO<Tab>SO_SERIAL. The "Số thuê bao" should have 10 digit, and the "Serial" should have 16 digit. I need only result, without any explain. If not found, just return empty string.`,
        ...mappingPhotos,
    ];
    const result = await generateContent(prompt);
    return result;
}

module.exports = { gemini_ocr };
