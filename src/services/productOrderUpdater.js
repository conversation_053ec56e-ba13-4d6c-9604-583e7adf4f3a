const PuppeteerHelper = require('./puppeteerHelper');
const { cacheProductOrders, cachePhoneItems } = require('../cache/redis');

async function updateProductOrders(prisma, redis, logger, storages = null) {
    // Fetch storages if not provided
    const targetStorages = storages || await prisma.storage.findMany({
        where: { status: 'active' },
    });

    for (const storage of targetStorages) {
        let puppeteer;
        try {
            logger.info(`Updating product orders for storage: ${storage.code}`);
            puppeteer = PuppeteerHelper.getInstance(storage.username, storage.password, storage.otp);
            await puppeteer.init();

            // Fetch product orders
            const orders = await puppeteer.get_product_orders();
            const productOrders = [];

            for (const order of orders) {
                try {
                    // Fetch order details
                    const details = await puppeteer.get_product_order_detail(order.id);

                    // Upsert product order
                    const upsertedOrder = await prisma.productOrder.upsert({
                        where: { id: order.id },
                        update: {
                            name: order.name,
                            status: order.status,
                            tariffId: details.orderInfoList[0]?.tariffId || '',
                            orderInfoId: details.orderInfoList[0]?.id || '',
                            tariffName: details.orderInfoList[0]?.tariffName || '',
                            pckCode: details.orderInfoList[0]?.pckCode || '',
                            orderDate: new Date(order.orderDate),
                            storageId: storage.id,
                        },
                        create: {
                            id: order.id,
                            name: order.name,
                            status: order.status,
                            tariffId: details.orderInfoList[0]?.tariffId || '',
                            orderInfoId: details.orderInfoList[0]?.id || '',
                            tariffName: details.orderInfoList[0]?.tariffName || '',
                            pckCode: details.orderInfoList[0]?.pckCode || '',
                            orderDate: new Date(order.orderDate),
                            storageId: storage.id,
                        },
                    });

                    productOrders.push(upsertedOrder);

                    // Upsert phone items
                    const phoneItems = details.orderDetailList || [];
                    for (const phone of phoneItems) {
                        await prisma.phoneItem.upsert({
                            where: { id: phone.id },
                            update: {
                                phoneNumber: phone.phoneNumber,
                                status: phone.status,
                                addedDate: phone.addedDate ? new Date(phone.addedDate) : null,
                                tariffCode: phone.tariffCode || '',
                                productOrderId: order.id,
                            },
                            create: {
                                id: phone.id,
                                phoneNumber: phone.phoneNumber,
                                status: phone.status,
                                addedDate: phone.addedDate ? new Date(phone.addedDate) : null,
                                tariffCode: phone.tariffCode || '',
                                productOrderId: order.id,
                            },
                        });
                    }

                    // Cache phone items
                    await cachePhoneItems(order.id, phoneItems);
                } catch (error) {
                    logger.warn(`Failed to process order ${order.id} for storage ${storage.code}: ${error.message}`);
                    continue; // Continue with next order
                }
            }

            // Cache product orders
            await cacheProductOrders(storage.id, productOrders);

            logger.info(`Completed update for storage: ${storage.code}`);
        } catch (error) {
            logger.error(`Failed to update product orders for storage ${storage.code}: ${error.message}`);
            throw error; // Rethrow to be caught by caller
        } finally {
            if (puppeteer) {
                await puppeteer.close();
            }
        }
    }
}

module.exports = { updateProductOrders };