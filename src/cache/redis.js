const { redis } = require('../shared/instances');

const cacheProductOrders = async (storageId, productOrders) => {
    const key = `product_orders:${storageId}`;
    await redis.set(key, JSON.stringify(productOrders), 'EX', 3600); // Cache for 1 hour
};

const getCachedProductOrders = async (storageId) => {
    const key = `product_orders:${storageId}`;
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
};

const cachePhoneItems = async (productOrderId, phoneItems) => {
    const key = `phone_items:${productOrderId}`;
    await redis.set(key, JSON.stringify(phoneItems), 'EX', 3600);
};

const getCachedPhoneItems = async (productOrderId) => {
    const key = `phone_items:${productOrderId}`;
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
};

module.exports = { cacheProductOrders, getCachedProductOrders, cachePhoneItems, getCachedPhoneItems };