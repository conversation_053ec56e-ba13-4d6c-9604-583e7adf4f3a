const { gemini_ocr } = require('../services/ocr');
const { authenticateWithUID } = require('../middleware/auth');

module.exports = async (fastify) => {

    fastify.post('/scan_photos', {
        preHandler: authenticateWithUID,
        schema: {
            consumes: ['multipart/form-data'],
        },
    }, async (request, reply) => {

        const data = await request.parts();
        const files = [];

        for await (const part of data) {
            if (part.type === 'file') {
                if (!['image/jpeg', 'image/png'].includes(part.mimetype)) {
                    return reply.code(400).send({ code: 400, message: 'Only JPEG/PNG images allowed' });
                }
                files.push({ buffer: await part.toBuffer(), filename: part.filename });
            }
        }

        if (files.length === 0) {
            return reply.code(400).send({ code: 400, message: 'No files provided' });
        }

        const result = await gemini_ocr(files.map(f => f.buffer));

        return {
            code: 200,
            message: 'Scan photos completed',
            data: result,
        };
    });
};