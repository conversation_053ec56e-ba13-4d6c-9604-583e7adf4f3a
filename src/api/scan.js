const { prisma, redis } = require('../shared/instances');
const { Queue } = require('bullmq');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const { gemini_ocr } = require('../services/ocr');
const { authenticateWithUID } = require('../middleware/auth');

module.exports = async (fastify) => {
    const scanQueue = new Queue('scan_photos', { connection: redis });

    fastify.post('/scan_photos', {
        preHandler: authenticateWithUID,
        schema: {
            consumes: ['multipart/form-data'],
        },
    }, async (request, reply) => {
        // Get authenticated user from middleware
        const user = request.user;
        const uid = user.uid;

        const data = await request.parts();
        const files = [];

        for await (const part of data) {
            if (part.type === 'file') {
                if (!['image/jpeg', 'image/png'].includes(part.mimetype)) {
                    return reply.code(400).send({ code: 400, message: 'Only JPEG/PNG images allowed' });
                }
                files.push({ buffer: await part.toBuffer(), filename: part.filename });
            }
        }

        if (files.length === 0) {
            return reply.code(400).send({ code: 400, message: 'No files provided' });
        }

        // Create task
        const taskId = uuidv4();
        await prisma.task.create({
            data: {
                id: taskId,
                type: 'scan_photos',
                status: 'processing',
                data: { uid, files: files.map(f => f.filename) },
            },
        });

        // Enqueue job
        await scanQueue.add('scan_photos', {
            taskId,
            uid,
            files,
        });

        return {
            code: 200,
            message: 'Scan photos task created',
            data: { task_id: taskId, message: 'Task enqueued' },
        };
    });
};