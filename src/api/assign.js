const { prisma, redis } = require('../shared/instances');
const { Queue } = require('bullmq');
const { v4: uuidv4 } = require('uuid');
const { cachePhoneItems, getCachedProductOrders, cacheProductOrders } = require('../cache/redis');

module.exports = async (fastify) => {
    const assignQueue = new Queue('assign_phone', { connection: redis });

    fastify.post('/assign_phone', {
        schema: {
            body: {
                type: 'object',
                required: ['phone_numbers', 'storage_code', 'uid'],
                properties: {
                    phone_numbers: { type: 'array', items: { type: 'string' } },
                    storage_code: { type: 'string' },
                    uid: { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { phone_numbers, storage_code, uid } = request.body;

        // Authenticate user
        const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
        if (!storage) {
            return reply.code(404).send({ code: 404, message: 'Storage not found' });
        }

        let user = await prisma.user.findFirst({
            where: { storageId: storage.id, uid, status: 'active' },
        });

        if (!user) {
            return reply.code(401).send({ code: 401, message: 'Unauthorized' });
        }

        user = await prisma.user.findUnique({
            where: { phoneNumber_storageId: { phoneNumber: user.phoneNumber, storageId: storage.id } },
        });

        if (!user || user.uid !== uid || user.status !== 'active') {
            return reply.code(401).send({ code: 401, message: 'Unauthorized' });
        }

        // OPTIMIZED CACHING STRATEGY:
        // 1. Try to get product orders from cache first (avoids DB query for product orders)
        let productOrders = await getCachedProductOrders(storage.id);

        if (!productOrders) {
            // Cache miss - fetch from database and cache
            productOrders = await prisma.productOrder.findMany({
                where: { storageId: storage.id },
                select: { id: true, name: true, status: true, tariffId: true, orderInfoId: true, tariffName: true, pckCode: true, orderDate: true },
            });

            // Cache the product orders for future requests
            await cacheProductOrders(storage.id, productOrders);
        }

        // 2. Directly query for phone items that match our phone numbers
        // This is more efficient than checking cache for every product order individually
        // because we only fetch what we need instead of loading all phone items from cache
        let existingPhones = await prisma.phoneItem.findMany({
            where: {
                phoneNumber: { in: phone_numbers },
                productOrder: { storageId: storage.id }
            },
            include: { productOrder: true },
        });

        // Cache the phone items we just fetched, grouped by product order
        const phoneItemsByProductOrder = {};
        existingPhones.forEach(phone => {
            if (!phoneItemsByProductOrder[phone.productOrderId]) {
                phoneItemsByProductOrder[phone.productOrderId] = [];
            }
            phoneItemsByProductOrder[phone.productOrderId].push(phone);
        });

        // Cache each product order's phone items (this will help future requests)
        for (const [productOrderId, phoneItems] of Object.entries(phoneItemsByProductOrder)) {
            await cachePhoneItems(productOrderId, phoneItems);
        }

        const notFoundPhones = phone_numbers.filter(
            (pn) => !existingPhones.some((ep) => ep.phoneNumber === pn)
        );

        // Group by product order
        for (const phone of existingPhones) {
            if (!phoneGroups[phone.productOrderId]) {
                phoneGroups[phone.productOrderId] = {
                    productOrder: phone.productOrder,
                    phoneNumbers: [],
                };
            }
            phoneGroups[phone.productOrderId].phoneNumbers.push(phone);
        }

        // Create task
        const taskId = uuidv4();
        await prisma.task.create({
            data: {
                id: taskId,
                type: 'assign_phone',
                status: 'pending',
                data: { phone_numbers, storage_code, uid, notFoundPhones, phoneGroups },
            },
        });

        // Enqueue jobs
        for (const [productOrderId, group] of Object.entries(phoneGroups)) {
            await assignQueue.add('assign_phone', {
                taskId,
                productOrderId,
                phoneNumbers: group.phoneNumbers.map((p) => p.phoneNumber),
                storage_code,
                uid,
            });
        }

        return {
            code: 200,
            message: 'Phone assignment task created',
            data: { task_id: taskId, message: 'Task enqueued' },
        };
    });

    fastify.get('/check_assign_status', {
        schema: {
            querystring: {
                type: 'object',
                required: ['task_id'],
                properties: {
                    task_id: { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { task_id } = request.query;

        const task = await prisma.task.findUnique({ where: { id: task_id } });
        if (!task) {
            return reply.code(404).send({ code: 404, message: 'Task not found' });
        }

        const response = {
            code: 200,
            message: 'Task status retrieved',
            data: {
                status: task.status,
                message: task.message || '',
                result: task.result || [],
            },
        };

        // Clean up completed tasks
        if (task.status === 'success' || task.status === 'error') {
            await assignQueue.removeJobs(task_id);
            await prisma.task.delete({ where: { id: task_id } });
        }

        return response;
    });
};