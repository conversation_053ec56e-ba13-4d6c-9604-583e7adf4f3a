const { prisma, redis } = require('../shared/instances');
const { Queue } = require('bullmq');
const { cachePhoneItems } = require('../cache/redis');
const { createWorkerForProductOrder } = require('../workers/assignPhoneWorker');
const { authenticateWithUID } = require('../middleware/auth');
const padTo2Digits = (num) => {
    return num.toString().padStart(2, '0');
};
const formatDate = (date) => {
    return (
        [
            padTo2Digits(date.getDate()),
            padTo2Digits(date.getMonth() + 1), // Months are zero-based
            date.getFullYear(),
        ].join('/') +
        ' ' +
        [
            padTo2Digits(date.getHours()),
            padTo2Digits(date.getMinutes()),
            padTo2Digits(date.getSeconds()),
        ].join(':')
    );
};
module.exports = async (fastify) => {

    fastify.post('/assign_phone', {
        preHandler: authenticateWithUID,
        schema: {
            body: {
                type: 'object',
                required: ['phone_numbers', 'storage_code'],
                properties: {
                    phone_numbers: { type: 'array', items: { type: 'string' } },
                    storage_code: { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { phone_numbers, storage_code } = request.body;

        // Get authenticated user and storage from middleware
        const user = request.user;
        const storage = request.storage;
        const uid = user.uid;

        // Validate that the storage_code matches the authenticated user's storage
        if (storage.code !== storage_code) {
            return reply.code(403).send({
                code: 403,
                message: 'Storage code does not match authenticated user'
            });
        }

        // LOGIC AS PER REQUIREMENTS:
        // 1. Filter phone_numbers NOT found in storage
        // 2. With phone_numbers existed, each phone_numbers:
        //    - Filter phone_items with status = 2 and phone_number = phone_number
        //    - If not found => error
        //    - If found => each found items
        //      - Check product_order.status = 2 => continue (skip)
        //      - If not => add to list (naming phones_to_assign)
        // 3. Each phones_to_assign => group by product_order => enqueue

        // Step 1: Find all phone items in this storage that match our phone numbers
        const existingPhones = await prisma.phoneItem.findMany({
            where: {
                phoneNumber: { in: phone_numbers },
                productOrder: { storageId: storage.id }
            },
            include: { productOrder: true },
        });

        // Step 2: Filter phone_numbers NOT found in storage
        const foundPhoneNumbers = [...new Set(existingPhones.map(p => p.phoneNumber))];
        const notFoundPhones = phone_numbers.filter(pn => !foundPhoneNumbers.includes(pn));
        const existingPhoneNumbers = phone_numbers.filter(pn => foundPhoneNumbers.includes(pn));

        // Step 3: Build phones_to_assign list
        const phones_to_assign = [];
        const errors = [];

        for (const phoneNumber of existingPhoneNumbers) {
            // Filter phone_items with status = 2 and phone_number = phone_number
            const phoneItemsWithStatus2 = existingPhones.filter(
                p => p.phoneNumber === phoneNumber && p.status === 2
            );

            // If not found => error
            if (phoneItemsWithStatus2.length === 0) {
                errors.push({
                    phone_number: phoneNumber,
                    status: 'error',
                    message: 'Phone not found with status = 2 or already assigned'
                });
                continue;
            }

            // Track if any items were added for this phone number
            let itemsAddedForPhone = 0;

            // If found => each found items
            for (const phoneItem of phoneItemsWithStatus2) {
                // Check product_order.status = 2 => continue (skip)
                if (phoneItem.productOrder.status === 2) {
                    continue; // Skip this item
                }

                // If not => add to list (naming phones_to_assign)
                phones_to_assign.push({
                    phoneNumber: phoneNumber,
                    phoneItem: phoneItem,
                    productOrder: phoneItem.productOrder
                });
                itemsAddedForPhone++;
            }

            // If no items were added for this phone (all had product_order.status = 2)
            // Check for already assigned phones (status = 1) to provide better error message
            if (itemsAddedForPhone === 0) {
                const assignedPhones = existingPhones.filter(
                    p => p.phoneNumber === phoneNumber && p.status === 1
                );

                if (assignedPhones.length > 0) {
                    // Get the latest assigned phone by addedDate
                    const latestAssigned = assignedPhones.sort((a, b) =>
                        new Date(b.addedDate) - new Date(a.addedDate)
                    )[0];

                    errors.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: `Phone already assigned with ${latestAssigned.tariffCode || 'unknown tariff'} at ${latestAssigned.addedDate ? formatDate(new Date(latestAssigned.addedDate)) : 'unknown date'}`
                    });
                } else {
                    // No assigned phones found, use generic message
                    errors.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: 'Phone not available for assignment (all product orders have status = 2)'
                    });
                }
            }
        }

        // Step 3.5: If there are multiple items with same phoneNumber but different productOrder,
        // only keep the item with the latest productOrder (ordered by orderDate)
        const phoneNumberMap = new Map();

        for (const item of phones_to_assign) {
            const phoneNumber = item.phoneNumber;

            if (!phoneNumberMap.has(phoneNumber)) {
                phoneNumberMap.set(phoneNumber, item);
            } else {
                const existingItem = phoneNumberMap.get(phoneNumber);

                // Compare orderDate - keep the one with latest orderDate
                if (new Date(item.productOrder.orderDate) > new Date(existingItem.productOrder.orderDate)) {
                    phoneNumberMap.set(phoneNumber, item);
                }
            }
        }

        // Replace phones_to_assign with deduplicated items (latest orderDate per phone)
        const deduplicatedPhonestoAssign = Array.from(phoneNumberMap.values());
        phones_to_assign.length = 0; // Clear original array
        phones_to_assign.push(...deduplicatedPhonestoAssign);

        // Step 4: Group phones_to_assign by product_order
        const phoneGroups = {};
        for (const item of phones_to_assign) {
            const productOrderId = item.productOrder.id;
            if (!phoneGroups[productOrderId]) {
                phoneGroups[productOrderId] = {
                    productOrder: item.productOrder,
                    phoneNumbers: [],
                };
            }
            // Avoid duplicates
            if (!phoneGroups[productOrderId].phoneNumbers.includes(item.phoneNumber)) {
                phoneGroups[productOrderId].phoneNumbers.push(item.phoneNumber);
            }
        }

        // Cache phone items for performance
        const phoneItemsByProductOrder = {};
        existingPhones.forEach(phone => {
            if (!phoneItemsByProductOrder[phone.productOrderId]) {
                phoneItemsByProductOrder[phone.productOrderId] = [];
            }
            phoneItemsByProductOrder[phone.productOrderId].push(phone);
        });

        for (const [productOrderId, phoneItems] of Object.entries(phoneItemsByProductOrder)) {
            await cachePhoneItems(productOrderId, phoneItems);
        }

        // Prepare initial task result with failed cases
        const initialResult = [];

        // Add not found phone numbers as failed cases
        for (const phoneNumber of notFoundPhones) {
            initialResult.push({
                phone_number: phoneNumber,
                status: 'error',
                message: 'Phone number not found in storage'
            });
        }

        // Add errors from status=2 filtering as failed cases
        initialResult.push(...errors);

        // Create task with initial failed results (let Prisma auto-generate the ID)
        const task = await prisma.task.create({
            data: {
                type: 'assign_phone',
                status: 'pending',
                data: {
                    phone_numbers,
                    storage_code,
                    uid,
                    notFoundPhones,
                    errors,
                    phones_to_assign: phones_to_assign.length,
                    phoneGroups: Object.keys(phoneGroups).length
                },
                result: initialResult, // Initialize with failed cases
            },
        });

        const taskId = task.id;

        // Step 5: Each group => enqueue to queue to background job
        // Each product order have one queue line (FIFO per product_order_id)
        // If queue of product_order_id ABC running, new requests wait until done
        if (Object.keys(phoneGroups).length > 0) {
            for (const [productOrderId, group] of Object.entries(phoneGroups)) {
                // Ensure worker exists for this product order (creates if not exists)
                createWorkerForProductOrder(productOrderId);

                // Create/use a separate queue for each product_order_id to ensure FIFO
                const productOrderQueue = new Queue(`assign_phone_${productOrderId}`, { connection: redis });

                await productOrderQueue.add('assign_phone', {
                    taskId,
                    productOrderId,
                    phoneNumbers: group.phoneNumbers,
                    storage_code,
                    uid,
                }, {
                    // Ensure FIFO processing for this product order
                    removeOnComplete: 10, // Keep last 10 completed jobs for debugging
                    removeOnFail: 50,     // Keep last 50 failed jobs for debugging
                    // Additional options to ensure FIFO
                    delay: 0,             // No delay
                    priority: 0,          // Default priority (FIFO within same priority)
                });
            }
        } else {
            // No phones to assign - mark task as completed with only failed cases
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: initialResult.length > 0 ? 'error' : 'success',
                    message: 'No phones to assign - all phones were filtered out',
                },
            });
        }

        return {
            code: 200,
            message: 'Phone assignment task created',
            data: { task_id: taskId, message: 'Task enqueued' },
        };
    });

    fastify.get('/check_assign_status', {
        schema: {
            querystring: {
                type: 'object',
                required: ['task_id'],
                properties: {
                    task_id: { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { task_id } = request.query;

        const task = await prisma.task.findUnique({ where: { id: task_id } });
        if (!task) {
            return reply.code(404).send({ code: 404, message: 'Task not found' });
        }

        const response = {
            code: 200,
            message: 'Task status retrieved',
            data: {
                status: task.status,
                message: task.message || '',
                result: task.result || [],
            },
        };

        // Keep completed tasks for future reference
        // Users can retrieve results anytime using the task_id
        // Optional: Add cleanup logic with TTL or manual cleanup endpoint

        return response;
    });
};