const { prisma, redis } = require('../shared/instances');
const { updateProductOrders } = require('../services/productOrderUpdater');
const { authenticateWithAPIKey } = require('../middleware/auth');

module.exports = async (fastify) => {
    fastify.post('/update_product_orders', {
        preHandler: authenticateWithAPIKey,
        schema: {
            body: {
                type: 'object',
                properties: {
                    storage_code: { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { storage_code } = request.body || {};

        // Get authenticated storage from middleware
        const authenticatedStorage = request.storage;

        // Fetch storages based on authentication and request
        let storages;
        if (storage_code) {
            // If storage_code is provided, validate it matches authenticated storage
            if (authenticatedStorage.code !== storage_code) {
                return reply.code(403).send({
                    code: 403,
                    message: 'Storage code does not match authenticated storage'
                });
            }

            if (authenticatedStorage.status !== 'active') {
                return reply.code(400).send({ code: 400, message: 'Storage is not active' });
            }
            storages = [authenticatedStorage];
        } else {
            // If no storage_code provided, only update the authenticated storage
            if (authenticatedStorage.status !== 'active') {
                return reply.code(400).send({ code: 400, message: 'Storage is not active' });
            }
            storages = [authenticatedStorage];
        }

        if (storages.length === 0) {
            return reply.code(404).send({ code: 404, message: 'No active storages found' });
        }

        // Perform update
        const results = [];
        for (const storage of storages) {
            try {
                fastify.log.info(`Manually updating product orders for storage: ${storage.code}`);
                await updateProductOrders(prisma, redis, fastify.log, [storage]); // Pass single storage
                results.push({
                    storage_code: storage.code,
                    status: 'success',
                    message: 'Product orders updated successfully',
                });
            } catch (error) {
                fastify.log.error(`Failed to update product orders for storage ${storage.code}: ${error.message}`);
                results.push({
                    storage_code: storage.code,
                    status: 'error',
                    message: error.message,
                });
                // Update storage status
                await prisma.storage.update({
                    where: { id: storage.id },
                    data: { status: 'error', message: error.message },
                });
            }
        }

        return {
            code: 200,
            message: 'Product order update completed',
            data: {
                results,
                summary: {
                    total: storages.length,
                    succeeded: results.filter(r => r.status === 'success').length,
                    failed: results.filter(r => r.status === 'error').length,
                },
            },
        };
    });
};