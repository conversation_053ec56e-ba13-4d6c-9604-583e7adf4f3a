const { prisma, redis } = require('../shared/instances');
const config = require('../config');
const { updateProductOrders } = require('../services/productOrderUpdater');

module.exports = async (fastify) => {
    fastify.post('/update_product_orders', {
        schema: {
            body: {
                type: 'object',
                properties: {
                    storage_code: { type: 'string' },
                },
            },
            headers: {
                type: 'object',
                required: ['x-api-key'],
                properties: {
                    'x-api-key': { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { storage_code } = request.body || {};
        const apiKey = request.headers['x-api-key'];

        // Validate API key
        if (apiKey !== config.apiKey) {
            return reply.code(401).send({ code: 401, message: 'Invalid API key' });
        }

        // Fetch storages
        let storages;
        if (storage_code) {
            const storage = await prisma.storage.findUnique({
                where: { code: storage_code },
            });
            if (!storage) {
                return reply.code(404).send({ code: 404, message: 'Storage not found' });
            }
            if (storage.status !== 'active') {
                return reply.code(400).send({ code: 400, message: 'Storage is not active' });
            }
            storages = [storage];
        } else {
            storages = await prisma.storage.findMany({
                where: { status: 'active' },
            });
        }

        if (storages.length === 0) {
            return reply.code(404).send({ code: 404, message: 'No active storages found' });
        }

        // Perform update
        const results = [];
        for (const storage of storages) {
            try {
                fastify.log.info(`Manually updating product orders for storage: ${storage.code}`);
                await updateProductOrders(prisma, redis, fastify.log, [storage]); // Pass single storage
                results.push({
                    storage_code: storage.code,
                    status: 'success',
                    message: 'Product orders updated successfully',
                });
            } catch (error) {
                fastify.log.error(`Failed to update product orders for storage ${storage.code}: ${error.message}`);
                results.push({
                    storage_code: storage.code,
                    status: 'error',
                    message: error.message,
                });
                // Update storage status
                await prisma.storage.update({
                    where: { id: storage.id },
                    data: { status: 'error', message: error.message },
                });
            }
        }

        return {
            code: 200,
            message: 'Product order update completed',
            data: {
                results,
                summary: {
                    total: storages.length,
                    succeeded: results.filter(r => r.status === 'success').length,
                    failed: results.filter(r => r.status === 'error').length,
                },
            },
        };
    });
};