const jwt = require('jsonwebtoken');
const { prisma } = require('../shared/instances');
const config = require('../config');
const { authenticateWithAPIKey } = require('../middleware/auth');

module.exports = async (fastify) => {
    fastify.post('/add-user', {
        preHandler: authenticateWithAPIKey,
        schema: {
            body: {
                type: 'object',
                required: ['phone_number', 'storage_code'],
                properties: {
                    phone_number: { type: 'string' },
                    storage_code: { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { phone_number, storage_code } = request.body;

        // Get authenticated storage from middleware
        const storage = request.storage;

        // Validate that the storage_code matches the authenticated storage
        if (storage.code !== storage_code) {
            return reply.code(403).send({
                code: 403,
                message: 'Storage code does not match authenticated storage'
            });
        }

        const existingUser = await prisma.user.findUnique({
            where: { phoneNumber_storageId: { phoneNumber: phone_number, storageId: storage.id } },
        });

        if (existingUser) {
            return reply.code(400).send({ code: 400, message: 'Phone number already exists in this storage' });
        }

        const user = await prisma.user.create({
            data: {
                phoneNumber: phone_number,
                storageId: storage.id,
                status: 'not_active',
            },
        });

        return {
            code: 200,
            message: 'User added successfully',
            data: {
                id: user.id,
                phone_number: user.phoneNumber,
                storage_code,
                status: user.status,
                created_at: user.createdAt,
                updated_at: user.updatedAt,
            },
        };
    });
};