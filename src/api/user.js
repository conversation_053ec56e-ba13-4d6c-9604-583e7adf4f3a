const jwt = require('jsonwebtoken');
const { prisma } = require('../shared/instances');
const config = require('../config');

module.exports = async (fastify) => {
    fastify.post('/add-user', {
        schema: {
            body: {
                type: 'object',
                required: ['phone_number', 'storage_code'],
                properties: {
                    phone_number: { type: 'string' },
                    storage_code: { type: 'string' },
                },
            },
            headers: {
                type: 'object',
                required: ['x-api-key'],
                properties: {
                    'x-api-key': { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { phone_number, storage_code } = request.body;
        const apiKey = request.headers['x-api-key'];

        if (apiKey !== config.apiKey) {
            return reply.code(401).send({ code: 401, message: 'Invalid API key' });
        }

        const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
        if (!storage) {
            return reply.code(404).send({ code: 404, message: 'Storage not found' });
        }

        const existingUser = await prisma.user.findUnique({
            where: { phoneNumber_storageId: { phoneNumber: phone_number, storageId: storage.id } },
        });

        if (existingUser) {
            return reply.code(400).send({ code: 400, message: 'Phone number already exists in this storage' });
        }

        const user = await prisma.user.create({
            data: {
                phoneNumber: phone_number,
                storageId: storage.id,
                status: 'not_active',
            },
        });

        return {
            code: 200,
            message: 'User added successfully',
            data: {
                id: user.id,
                phone_number: user.phoneNumber,
                storage_code,
                status: user.status,
                created_at: user.createdAt,
                updated_at: user.updatedAt,
            },
        };
    });
};