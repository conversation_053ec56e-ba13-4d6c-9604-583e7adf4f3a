const { prisma } = require('../shared/instances');
const { authenticateWithAP<PERSON><PERSON>ey } = require('../middleware/auth');

module.exports = async (fastify) => {
    // Cleanup old completed tasks (admin only)
    fastify.delete('/cleanup_tasks', {
        preHandler: authenticateWith<PERSON><PERSON><PERSON>ey,
        schema: {
            querystring: {
                type: 'object',
                properties: {
                    older_than_days: { type: 'integer', default: 7 },
                    status: { type: 'string', enum: ['success', 'error', 'all'] },
                },
            },
        },
    }, async (request, reply) => {
        const { older_than_days = 7, status } = request.query;
        const storage = request.storage;

        // Calculate cutoff date
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - older_than_days);

        // Build where clause
        const whereClause = {
            updatedAt: { lt: cutoffDate },
            data: {
                path: ['storage_code'],
                equals: storage.code
            }
        };

        if (status && status !== 'all') {
            whereClause.status = status;
        } else {
            whereClause.status = { in: ['success', 'error'] }; // Don't delete pending tasks
        }

        // Delete old tasks
        const deletedTasks = await prisma.task.deleteMany({
            where: whereClause
        });

        return {
            code: 200,
            message: 'Task cleanup completed',
            data: {
                deleted_count: deletedTasks.count,
                cutoff_date: cutoffDate,
                storage_code: storage.code
            }
        };
    });

    // Get task history for a storage (admin only)
    fastify.get('/task_history', {
        preHandler: authenticateWithAPIKey,
        schema: {
            querystring: {
                type: 'object',
                properties: {
                    limit: { type: 'integer', default: 50, maximum: 200 },
                    offset: { type: 'integer', default: 0 },
                    status: { type: 'string', enum: ['pending', 'success', 'error', 'all'] },
                    type: { type: 'string', enum: ['assign_phone', 'upload_phone', 'scan_photos', 'all'] },
                },
            },
        },
    }, async (request, reply) => {
        const { limit = 50, offset = 0, status, type } = request.query;
        const storage = request.storage;

        // Build where clause
        const whereClause = {
            data: {
                path: ['storage_code'],
                equals: storage.code
            }
        };

        if (status && status !== 'all') {
            whereClause.status = status;
        }

        if (type && type !== 'all') {
            whereClause.type = type;
        }

        // Get tasks with pagination
        const [tasks, total] = await Promise.all([
            prisma.task.findMany({
                where: whereClause,
                orderBy: { createdAt: 'desc' },
                take: limit,
                skip: offset,
                select: {
                    id: true,
                    type: true,
                    status: true,
                    message: true,
                    result: true,
                    createdAt: true,
                    updatedAt: true
                }
            }),
            prisma.task.count({ where: whereClause })
        ]);

        return {
            code: 200,
            message: 'Task history retrieved',
            data: {
                tasks,
                pagination: {
                    total,
                    limit,
                    offset,
                    has_more: offset + limit < total
                }
            }
        };
    });
};
