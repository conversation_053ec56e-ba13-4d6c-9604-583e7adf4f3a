const { prisma } = require('../shared/instances');
const { authenticateWithUID, authenticate<PERSON>ithAPIKey } = require('../middleware/auth');

module.exports = async (fastify) => {
    // Get assign logs for a user (UID authentication)
    fastify.get('/assign_logs', {
        preHandler: authenticateWithUID,
        schema: {
            querystring: {
                type: 'object',
                properties: {
                    phone_number: { type: 'string' },
                    status: { type: 'string', enum: ['success', 'error', 'all'] },
                    product_order_id: { type: 'string' },
                    limit: { type: 'integer', default: 50, maximum: 200 },
                    offset: { type: 'integer', default: 0 },
                    date_from: { type: 'string', format: 'date' },
                    date_to: { type: 'string', format: 'date' },
                },
            },
        },
    }, async (request, reply) => {
        const { 
            phone_number, 
            status, 
            product_order_id, 
            limit = 50, 
            offset = 0,
            date_from,
            date_to 
        } = request.query;
        
        const user = request.user;
        const storage = request.storage;

        // Build where clause
        const whereClause = {
            userId: user.id,
            productOrder: {
                storageId: storage.id
            }
        };

        if (phone_number) {
            whereClause.phoneNumber = phone_number;
        }

        if (status && status !== 'all') {
            whereClause.status = status;
        }

        if (product_order_id) {
            whereClause.productOrderId = product_order_id;
        }

        if (date_from || date_to) {
            whereClause.createdAt = {};
            if (date_from) {
                whereClause.createdAt.gte = new Date(date_from);
            }
            if (date_to) {
                const toDate = new Date(date_to);
                toDate.setHours(23, 59, 59, 999); // End of day
                whereClause.createdAt.lte = toDate;
            }
        }

        try {
            // Get assign logs with pagination
            const [assignLogs, total] = await Promise.all([
                prisma.assignLog.findMany({
                    where: whereClause,
                    include: {
                        productOrder: {
                            select: {
                                id: true,
                                name: true,
                                status: true,
                                tariffName: true,
                                pckCode: true,
                                orderDate: true
                            }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    take: limit,
                    skip: offset,
                }),
                prisma.assignLog.count({ where: whereClause })
            ]);

            return {
                code: 200,
                message: 'Assign logs retrieved successfully',
                data: {
                    assign_logs: assignLogs.map(log => ({
                        id: log.id,
                        phone_number: log.phoneNumber,
                        status: log.status,
                        message: log.message,
                        product_order: {
                            id: log.productOrder.id,
                            name: log.productOrder.name,
                            status: log.productOrder.status,
                            tariff_name: log.productOrder.tariffName,
                            pck_code: log.productOrder.pckCode,
                            order_date: log.productOrder.orderDate
                        },
                        created_at: log.createdAt,
                        updated_at: log.updatedAt
                    })),
                    pagination: {
                        total,
                        limit,
                        offset,
                        has_more: offset + limit < total
                    },
                    summary: {
                        total_success: assignLogs.filter(log => log.status === 'success').length,
                        total_error: assignLogs.filter(log => log.status === 'error').length
                    }
                }
            };
        } catch (error) {
            return reply.code(500).send({
                code: 500,
                message: 'Failed to retrieve assign logs',
                error: error.message
            });
        }
    });

    // Get assign logs for admin (API Key authentication)
    fastify.get('/admin/assign_logs', {
        preHandler: authenticateWithAPIKey,
        schema: {
            querystring: {
                type: 'object',
                properties: {
                    phone_number: { type: 'string' },
                    status: { type: 'string', enum: ['success', 'error', 'all'] },
                    product_order_id: { type: 'string' },
                    user_id: { type: 'integer' },
                    limit: { type: 'integer', default: 50, maximum: 200 },
                    offset: { type: 'integer', default: 0 },
                    date_from: { type: 'string', format: 'date' },
                    date_to: { type: 'string', format: 'date' },
                },
            },
        },
    }, async (request, reply) => {
        const { 
            phone_number, 
            status, 
            product_order_id, 
            user_id,
            limit = 50, 
            offset = 0,
            date_from,
            date_to 
        } = request.query;
        
        const storage = request.storage;

        // Build where clause
        const whereClause = {
            productOrder: {
                storageId: storage.id
            }
        };

        if (phone_number) {
            whereClause.phoneNumber = phone_number;
        }

        if (status && status !== 'all') {
            whereClause.status = status;
        }

        if (product_order_id) {
            whereClause.productOrderId = product_order_id;
        }

        if (user_id) {
            whereClause.userId = user_id;
        }

        if (date_from || date_to) {
            whereClause.createdAt = {};
            if (date_from) {
                whereClause.createdAt.gte = new Date(date_from);
            }
            if (date_to) {
                const toDate = new Date(date_to);
                toDate.setHours(23, 59, 59, 999); // End of day
                whereClause.createdAt.lte = toDate;
            }
        }

        try {
            // Get assign logs with pagination
            const [assignLogs, total] = await Promise.all([
                prisma.assignLog.findMany({
                    where: whereClause,
                    include: {
                        productOrder: {
                            select: {
                                id: true,
                                name: true,
                                status: true,
                                tariffName: true,
                                pckCode: true,
                                orderDate: true
                            }
                        },
                        user: {
                            select: {
                                id: true,
                                phoneNumber: true,
                                name: true,
                                uid: true
                            }
                        }
                    },
                    orderBy: { createdAt: 'desc' },
                    take: limit,
                    skip: offset,
                }),
                prisma.assignLog.count({ where: whereClause })
            ]);

            return {
                code: 200,
                message: 'Assign logs retrieved successfully',
                data: {
                    assign_logs: assignLogs.map(log => ({
                        id: log.id,
                        phone_number: log.phoneNumber,
                        status: log.status,
                        message: log.message,
                        product_order: {
                            id: log.productOrder.id,
                            name: log.productOrder.name,
                            status: log.productOrder.status,
                            tariff_name: log.productOrder.tariffName,
                            pck_code: log.productOrder.pckCode,
                            order_date: log.productOrder.orderDate
                        },
                        user: {
                            id: log.user.id,
                            phone_number: log.user.phoneNumber,
                            name: log.user.name,
                            uid: log.user.uid
                        },
                        created_at: log.createdAt,
                        updated_at: log.updatedAt
                    })),
                    pagination: {
                        total,
                        limit,
                        offset,
                        has_more: offset + limit < total
                    },
                    summary: {
                        total_success: assignLogs.filter(log => log.status === 'success').length,
                        total_error: assignLogs.filter(log => log.status === 'error').length
                    }
                }
            };
        } catch (error) {
            return reply.code(500).send({
                code: 500,
                message: 'Failed to retrieve assign logs',
                error: error.message
            });
        }
    });

    // Get assign log statistics (API Key authentication)
    fastify.get('/admin/assign_stats', {
        preHandler: authenticateWithAPIKey,
        schema: {
            querystring: {
                type: 'object',
                properties: {
                    date_from: { type: 'string', format: 'date' },
                    date_to: { type: 'string', format: 'date' },
                    group_by: { type: 'string', enum: ['day', 'week', 'month'], default: 'day' },
                },
            },
        },
    }, async (request, reply) => {
        const { date_from, date_to, group_by = 'day' } = request.query;
        const storage = request.storage;

        // Build where clause
        const whereClause = {
            productOrder: {
                storageId: storage.id
            }
        };

        if (date_from || date_to) {
            whereClause.createdAt = {};
            if (date_from) {
                whereClause.createdAt.gte = new Date(date_from);
            }
            if (date_to) {
                const toDate = new Date(date_to);
                toDate.setHours(23, 59, 59, 999);
                whereClause.createdAt.lte = toDate;
            }
        }

        try {
            // Get overall statistics
            const [totalLogs, successLogs, errorLogs] = await Promise.all([
                prisma.assignLog.count({ where: whereClause }),
                prisma.assignLog.count({ where: { ...whereClause, status: 'success' } }),
                prisma.assignLog.count({ where: { ...whereClause, status: 'error' } })
            ]);

            return {
                code: 200,
                message: 'Assign statistics retrieved successfully',
                data: {
                    summary: {
                        total_assignments: totalLogs,
                        successful_assignments: successLogs,
                        failed_assignments: errorLogs,
                        success_rate: totalLogs > 0 ? ((successLogs / totalLogs) * 100).toFixed(2) : 0
                    },
                    period: {
                        from: date_from || 'all time',
                        to: date_to || 'now',
                        group_by
                    }
                }
            };
        } catch (error) {
            return reply.code(500).send({
                code: 500,
                message: 'Failed to retrieve assign statistics',
                error: error.message
            });
        }
    });
};
