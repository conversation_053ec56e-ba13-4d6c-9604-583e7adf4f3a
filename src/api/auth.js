const { prisma } = require('../shared/instances');

module.exports = async (fastify) => {
    fastify.post('/auth', {
        schema: {
            body: {
                type: 'object',
                required: ['phone_number', 'storage_code', 'uid'],
                properties: {
                    phone_number: { type: 'string' },
                    storage_code: { type: 'string' },
                    uid: { type: 'string' },
                },
            },
        },
    }, async (request, reply) => {
        const { phone_number, storage_code, uid } = request.body;

        const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
        if (!storage) {
            return reply.code(404).send({ code: 404, message: 'Storage not found' });
        }

        const user = await prisma.user.findUnique({
            where: { phoneNumber_storageId: { phoneNumber: phone_number, storageId: storage.id } },
        });

        if (!user) {
            return reply.code(404).send({ code: 404, message: 'User not found' });
        }

        if (user.status === 'active' && user.uid !== uid) {
            return reply.code(400).send({ code: 400, message: 'UID mismatch for active user' });
        }

        if (user.status === 'not_active') {
            await prisma.user.update({
                where: { id: user.id },
                data: { uid, status: 'active' },
            });
        }

        return {
            code: 200,
            message: 'Authentication successful',
            data: {
                id: user.id,
                phone_number: user.phoneNumber,
                storage_code,
                status: user.status,
                created_at: user.createdAt,
                updated_at: user.updatedAt,
            },
        };
    });
};