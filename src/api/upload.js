const { prisma, redis } = require('../shared/instances');
const { Queue } = require('bullmq');
const { authenticateWithUID } = require('../middleware/auth');
const { gemini_ocr } = require('../services/ocr');
const { cachePhoneItems } = require('../cache/redis');
const { createWorkerForProductOrder } = require('../workers/assignPhoneWorker');

module.exports = async (fastify) => {
    fastify.post('/upload_phone', {
        preHandler: authenticateWithUID,
        schema: {
            consumes: ['multipart/form-data'],
        },
    }, async (request, reply) => {
        // Get authenticated user and storage from middleware
        const user = request.user;
        const storage = request.storage;
        const uid = user.uid;

        const data = await request.parts();
        const files = [];
        let storage_code;

        for await (const part of data) {
            if (part.type === 'file') {
                if (!['image/jpeg', 'image/png'].includes(part.mimetype)) {
                    return reply.code(400).send({ code: 400, message: 'Only JPEG/PNG images allowed' });
                }
                const buffer = await part.toBuffer();
                if (buffer.length > 5 * 1024 * 1024) {
                    return reply.code(400).send({ code: 400, message: 'File size exceeds 5MB' });
                }
                files.push({ buffer, filename: part.filename });
            } else {
                if (part.fieldname === 'storage_code') storage_code = part.value;
            }
        }

        if (!storage_code || files.length === 0) {
            return reply.code(400).send({ code: 400, message: 'Missing required fields' });
        }

        // Validate that the storage_code matches the authenticated user's storage
        if (storage.code !== storage_code) {
            return reply.code(403).send({
                code: 403,
                message: 'Storage code does not match authenticated user'
            });
        }

        try {
            // Step 1: OCR Processing - Extract phone numbers from uploaded images
            const ocrResults = [];
            const phone_numbers = [];

            for (const file of files) {
                try {
                    console.log(`Processing OCR for file: ${file.filename}`);
                    // Use OCR to extract phone numbers from the image
                    const extractedData = await gemini_ocr([file.buffer]);

                    if (extractedData && extractedData.length > 0) {
                        for (const result of extractedData) {
                            if (result.phone_number) {
                                phone_numbers.push(result.phone_number);
                                ocrResults.push({
                                    filename: file.filename,
                                    phone_number: result.phone_number,
                                    serial: result.serial,
                                    status: 'success',
                                    message: 'Phone number extracted successfully'
                                });
                            }
                        }
                    } else {
                        ocrResults.push({
                            filename: file.filename,
                            status: 'error',
                            message: 'No phone number found in image'
                        });
                    }
                } catch (error) {
                    console.error(`OCR failed for ${file.filename}:`, error);
                    ocrResults.push({
                        filename: file.filename,
                        status: 'error',
                        message: `OCR failed: ${error.message}`
                    });
                }
            }

            console.log(`OCR completed. Extracted phone numbers: ${phone_numbers}`);

            // Step 2: Apply Assignment Logic (same as assign_phone API)
            if (phone_numbers.length === 0) {
                return {
                    code: 200,
                    message: 'Upload completed but no phone numbers extracted',
                    data: {
                        ocr_results: ocrResults,
                        assignment_results: [],
                        summary: {
                            total_files: files.length,
                            successful_ocr: ocrResults.filter(r => r.status === 'success').length,
                            failed_ocr: ocrResults.filter(r => r.status === 'error').length,
                            phones_extracted: phone_numbers.length,
                            phones_assigned: 0
                        }
                    }
                };
            }

            // Apply the same logic as assign_phone API
            const existingPhones = await prisma.phoneItem.findMany({
                where: {
                    phoneNumber: { in: phone_numbers },
                    productOrder: { storageId: storage.id }
                },
                include: { productOrder: true },
            });

            const foundPhoneNumbers = [...new Set(existingPhones.map(p => p.phoneNumber))];
            const notFoundPhones = phone_numbers.filter(pn => !foundPhoneNumbers.includes(pn));
            const existingPhoneNumbers = phone_numbers.filter(pn => foundPhoneNumbers.includes(pn));

            const phones_to_assign = [];
            const errors = [];

            // Process existing phone numbers (same logic as assign_phone API)
            for (const phoneNumber of existingPhoneNumbers) {
                const phoneItemsWithStatus2 = existingPhones.filter(
                    p => p.phoneNumber === phoneNumber && p.status === 2
                );

                if (phoneItemsWithStatus2.length === 0) {
                    errors.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: 'Phone not found with status = 2 or already assigned'
                    });
                    continue;
                }

                let itemsAddedForPhone = 0;
                for (const phoneItem of phoneItemsWithStatus2) {
                    if (phoneItem.productOrder.status === 2) {
                        continue;
                    }

                    phones_to_assign.push({
                        phoneNumber: phoneNumber,
                        phoneItem: phoneItem,
                        productOrder: phoneItem.productOrder
                    });
                    itemsAddedForPhone++;
                }

                // Check for already assigned phones if no items added
                if (itemsAddedForPhone === 0) {
                    const assignedPhones = existingPhones.filter(
                        p => p.phoneNumber === phoneNumber && p.status === 1
                    );

                    if (assignedPhones.length > 0) {
                        const latestAssigned = assignedPhones.sort((a, b) =>
                            new Date(b.addedDate) - new Date(a.addedDate)
                        )[0];

                        errors.push({
                            phone_number: phoneNumber,
                            status: 'error',
                            message: `Phone already assigned with ${latestAssigned.tariffCode || 'unknown tariff'} at ${latestAssigned.addedDate ? new Date(latestAssigned.addedDate).toISOString() : 'unknown date'}`
                        });
                    } else {
                        errors.push({
                            phone_number: phoneNumber,
                            status: 'error',
                            message: 'Phone not available for assignment (all product orders have status = 2)'
                        });
                    }
                }
            }

            // Deduplicate by latest orderDate
            const phoneNumberMap = new Map();
            for (const item of phones_to_assign) {
                const phoneNumber = item.phoneNumber;
                if (!phoneNumberMap.has(phoneNumber)) {
                    phoneNumberMap.set(phoneNumber, item);
                } else {
                    const existingItem = phoneNumberMap.get(phoneNumber);
                    if (new Date(item.productOrder.orderDate) > new Date(existingItem.productOrder.orderDate)) {
                        phoneNumberMap.set(phoneNumber, item);
                    }
                }
            }
            const deduplicatedPhonestoAssign = Array.from(phoneNumberMap.values());

            // Add not found phones as errors
            for (const phoneNumber of notFoundPhones) {
                errors.push({
                    phone_number: phoneNumber,
                    status: 'error',
                    message: 'Phone number not found in storage'
                });
            }

            // Step 3: Create assignment task and enqueue jobs if there are phones to assign
            let assignmentResults = [...errors]; // Start with errors
            let assignmentTaskId = null;

            if (deduplicatedPhonestoAssign.length > 0) {
                // Group by product order
                const phoneGroups = {};
                for (const item of deduplicatedPhonestoAssign) {
                    const productOrderId = item.productOrder.id;
                    if (!phoneGroups[productOrderId]) {
                        phoneGroups[productOrderId] = {
                            productOrder: item.productOrder,
                            phoneNumbers: [],
                        };
                    }
                    if (!phoneGroups[productOrderId].phoneNumbers.includes(item.phoneNumber)) {
                        phoneGroups[productOrderId].phoneNumbers.push(item.phoneNumber);
                    }
                }

                // Cache phone items for performance
                const phoneItemsByProductOrder = {};
                existingPhones.forEach(phone => {
                    if (!phoneItemsByProductOrder[phone.productOrderId]) {
                        phoneItemsByProductOrder[phone.productOrderId] = [];
                    }
                    phoneItemsByProductOrder[phone.productOrderId].push(phone);
                });

                for (const [productOrderId, phoneItems] of Object.entries(phoneItemsByProductOrder)) {
                    await cachePhoneItems(productOrderId, phoneItems);
                }

                // Create assignment task
                const assignTask = await prisma.task.create({
                    data: {
                        type: 'assign_phone',
                        status: 'pending',
                        data: {
                            phone_numbers,
                            storage_code,
                            uid,
                            triggered_by_upload: true,
                            notFoundPhones,
                            errors,
                            phones_to_assign: deduplicatedPhonestoAssign.length,
                            phoneGroups: Object.keys(phoneGroups).length
                        },
                        result: errors, // Initialize with errors
                    },
                });

                assignmentTaskId = assignTask.id;

                // Enqueue assignment jobs
                for (const [productOrderId, group] of Object.entries(phoneGroups)) {
                    createWorkerForProductOrder(productOrderId);
                    const productOrderQueue = new Queue(`assign_phone_${productOrderId}`, { connection: redis });

                    await productOrderQueue.add('assign_phone', {
                        taskId: assignTask.id,
                        productOrderId,
                        phoneNumbers: group.phoneNumbers,
                        storage_code,
                        uid,
                    }, {
                        removeOnComplete: 10,
                        removeOnFail: 50,
                        delay: 0,
                        priority: 0,
                    });
                }

                assignmentResults.push({
                    assignment_task_id: assignTask.id,
                    status: 'success',
                    message: `Assignment task created for ${deduplicatedPhonestoAssign.length} phones`
                });
            }

            return {
                code: 200,
                message: 'Upload and assignment processing completed',
                data: {
                    ocr_results: ocrResults,
                    assignment_results: assignmentResults,
                    assignment_task_id: assignmentTaskId,
                    summary: {
                        total_files: files.length,
                        successful_ocr: ocrResults.filter(r => r.status === 'success').length,
                        failed_ocr: ocrResults.filter(r => r.status === 'error').length,
                        phones_extracted: phone_numbers.length,
                        phones_to_assign: deduplicatedPhonestoAssign.length,
                        assignment_errors: errors.length
                    }
                }
            };

        } catch (error) {
            console.error('Upload processing error:', error);
            return reply.code(500).send({
                code: 500,
                message: 'Upload processing failed',
                error: error.message
            });
        }
    });
};