const { prisma, redis } = require('../shared/instances');
const { Queue } = require('bullmq');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const { authenticateWithUID } = require('../middleware/auth');

module.exports = async (fastify) => {
    const uploadQueue = new Queue('upload_phone', { connection: redis });

    fastify.post('/upload_phone', {
        preHandler: authenticateWithUID,
        schema: {
            consumes: ['multipart/form-data'],
        },
    }, async (request, reply) => {
        // Get authenticated user and storage from middleware
        const user = request.user;
        const storage = request.storage;
        const uid = user.uid;

        const data = await request.parts();
        const files = [];
        let storage_code;

        for await (const part of data) {
            if (part.type === 'file') {
                if (!['image/jpeg', 'image/png'].includes(part.mimetype)) {
                    return reply.code(400).send({ code: 400, message: 'Only JPEG/PNG images allowed' });
                }
                const buffer = await part.toBuffer();
                if (buffer.length > 5 * 1024 * 1024) {
                    return reply.code(400).send({ code: 400, message: 'File size exceeds 5MB' });
                }
                files.push({ buffer, filename: part.filename });
            } else {
                if (part.fieldname === 'storage_code') storage_code = part.value;
            }
        }

        if (!storage_code || files.length === 0) {
            return reply.code(400).send({ code: 400, message: 'Missing required fields' });
        }

        // Validate that the storage_code matches the authenticated user's storage
        if (storage.code !== storage_code) {
            return reply.code(403).send({
                code: 403,
                message: 'Storage code does not match authenticated user'
            });
        }

        // Create task
        const taskId = uuidv4();
        await prisma.task.create({
            data: {
                id: taskId,
                type: 'upload_phone',
                status: 'pending',
                data: { storage_code, uid, files: files.map(f => f.filename) },
            },
        });

        // Enqueue job
        await uploadQueue.add('upload_phone', {
            taskId,
            storage_code,
            uid,
            files,
        });

        return {
            code: 200,
            message: 'Upload phone task created',
            data: { task_id: taskId, message: 'Task enqueued for OCR processing' },
        };
    });
};