const fastify = require('fastify')({ logger: true });
const { PrismaClient } = require('@prisma/client');
const Redis = require('ioredis');
const config = require('../config');

// Initialize dependencies
const prisma = new PrismaClient();
const redis = new Redis(config.redisUrl, {
    maxRetriesPerRequest: null, // Required for BullMQ
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
});

module.exports = { fastify, prisma, redis };
