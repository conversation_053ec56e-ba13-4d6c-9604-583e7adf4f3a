const { Worker } = require('bullmq');
const { prisma, redis } = require('../shared/instances');
const { gemini_ocr } = require('../services/ocr');
const { Queue } = require('bullmq');
const { createWorkerForProductOrder } = require('./assignPhoneWorker');

// Function to trigger phone assignment (similar to assign_phone API logic)
const triggerPhoneAssignment = async (phoneNumbers, storage_code, uid, uploadTaskId) => {
    try {
        // Get storage
        const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
        if (!storage) {
            throw new Error('Storage not found');
        }

        // Get user
        const user = await prisma.user.findFirst({
            where: { storageId: storage.id, uid, status: 'active' },
        });
        if (!user) {
            throw new Error('User not found or inactive');
        }

        // Apply the same logic as assign_phone API
        const existingPhones = await prisma.phoneItem.findMany({
            where: {
                phoneNumber: { in: phoneNumbers },
                productOrder: { storageId: storage.id }
            },
            include: { productOrder: true },
        });

        const foundPhoneNumbers = [...new Set(existingPhones.map(p => p.phoneNumber))];
        const notFoundPhones = phoneNumbers.filter(pn => !foundPhoneNumbers.includes(pn));
        const existingPhoneNumbers = phoneNumbers.filter(pn => foundPhoneNumbers.includes(pn));

        const phones_to_assign = [];
        const errors = [];

        // Process existing phone numbers (same logic as assign_phone API)
        for (const phoneNumber of existingPhoneNumbers) {
            const phoneItemsWithStatus2 = existingPhones.filter(
                p => p.phoneNumber === phoneNumber && p.status === 2
            );

            if (phoneItemsWithStatus2.length === 0) {
                errors.push({
                    phone_number: phoneNumber,
                    status: 'error',
                    message: 'Phone not found with status = 2 or already assigned'
                });
                continue;
            }

            let itemsAddedForPhone = 0;
            for (const phoneItem of phoneItemsWithStatus2) {
                if (phoneItem.productOrder.status === 2) {
                    continue;
                }

                phones_to_assign.push({
                    phoneNumber: phoneNumber,
                    phoneItem: phoneItem,
                    productOrder: phoneItem.productOrder
                });
                itemsAddedForPhone++;
            }

            // Check for already assigned phones if no items added
            if (itemsAddedForPhone === 0) {
                const assignedPhones = existingPhones.filter(
                    p => p.phoneNumber === phoneNumber && p.status === 1
                );

                if (assignedPhones.length > 0) {
                    const latestAssigned = assignedPhones.sort((a, b) =>
                        new Date(b.addedDate) - new Date(a.addedDate)
                    )[0];

                    errors.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: `Phone already assigned with ${latestAssigned.tariffCode || 'unknown tariff'} at ${latestAssigned.addedDate ? new Date(latestAssigned.addedDate).toISOString() : 'unknown date'}`
                    });
                } else {
                    errors.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: 'Phone not available for assignment (all product orders have status = 2)'
                    });
                }
            }
        }

        // Deduplicate by latest orderDate (same logic as assign_phone API)
        const phoneNumberMap = new Map();
        for (const item of phones_to_assign) {
            const phoneNumber = item.phoneNumber;
            if (!phoneNumberMap.has(phoneNumber)) {
                phoneNumberMap.set(phoneNumber, item);
            } else {
                const existingItem = phoneNumberMap.get(phoneNumber);
                if (new Date(item.productOrder.orderDate) > new Date(existingItem.productOrder.orderDate)) {
                    phoneNumberMap.set(phoneNumber, item);
                }
            }
        }
        const deduplicatedPhonestoAssign = Array.from(phoneNumberMap.values());

        // Add not found phones as errors
        for (const phoneNumber of notFoundPhones) {
            errors.push({
                phone_number: phoneNumber,
                status: 'error',
                message: 'Phone number not found in storage'
            });
        }

        // If there are phones to assign, create assignment task and enqueue
        if (deduplicatedPhonestoAssign.length > 0) {
            // Group by product order
            const phoneGroups = {};
            for (const item of deduplicatedPhonestoAssign) {
                const productOrderId = item.productOrder.id;
                if (!phoneGroups[productOrderId]) {
                    phoneGroups[productOrderId] = {
                        productOrder: item.productOrder,
                        phoneNumbers: [],
                    };
                }
                if (!phoneGroups[productOrderId].phoneNumbers.includes(item.phoneNumber)) {
                    phoneGroups[productOrderId].phoneNumbers.push(item.phoneNumber);
                }
            }

            // Create assignment task
            const assignTask = await prisma.task.create({
                data: {
                    type: 'assign_phone',
                    status: 'pending',
                    data: {
                        phone_numbers: phoneNumbers,
                        storage_code,
                        uid,
                        triggered_by_upload: uploadTaskId,
                        notFoundPhones,
                        errors,
                        phones_to_assign: deduplicatedPhonestoAssign.length,
                        phoneGroups: Object.keys(phoneGroups).length
                    },
                    result: errors, // Initialize with errors
                },
            });

            // Enqueue assignment jobs
            for (const [productOrderId, group] of Object.entries(phoneGroups)) {
                createWorkerForProductOrder(productOrderId);
                const productOrderQueue = new Queue(`assign_phone_${productOrderId}`, { connection: redis });

                await productOrderQueue.add('assign_phone', {
                    taskId: assignTask.id,
                    productOrderId,
                    phoneNumbers: group.phoneNumbers,
                    storage_code,
                    uid,
                }, {
                    removeOnComplete: 10,
                    removeOnFail: 50,
                    delay: 0,
                    priority: 0,
                });
            }

            return [
                ...errors,
                {
                    assignment_task_id: assignTask.id,
                    status: 'success',
                    message: `Assignment task created for ${deduplicatedPhonestoAssign.length} phones`
                }
            ];
        } else {
            // No phones to assign, return only errors
            return errors;
        }

    } catch (error) {
        console.error('Error in triggerPhoneAssignment:', error);
        throw error;
    }
};

const createUploadPhoneWorker = () => {
    const worker = new Worker('upload_phone', async (job) => {
        const { taskId, files, storage_code, uid } = job.data;

        console.log(`Processing upload job ${job.id} for task ${taskId}`);
        console.log(`Files count: ${files?.length || 0}, storage: ${storage_code}, uid: ${uid}`);

        try {
            // Fetch storage
            const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
            if (!storage) {
                throw new Error('Storage not found');
            }

            // Authenticate user
            const user = await prisma.user.findFirst({
                where: { storageId: storage.id, uid, status: 'active' },
            });

            if (!user) {
                throw new Error('Unauthorized user');
            }

            const results = [];
            
            // Process each uploaded file
            console.log(`Starting OCR processing for ${files.length} files`);
            for (const file of files) {
                try {
                    console.log(`Processing file: ${file.filename}`);
                    // Use OCR to extract phone numbers from the image (pass as array)
                    const ocrResults = await gemini_ocr([file.buffer]);
                    console.log(`OCR results for ${file.filename}:`, ocrResults);
                    
                    for (const result of ocrResults) {
                        results.push({
                            phone_number: result.phone_number,
                            serial: result.serial,
                            status: 'success',
                            message: 'Phone number extracted successfully',
                        });
                    }
                } catch (error) {
                    console.error(`OCR failed for ${file.filename}:`, error);
                    results.push({
                        filename: file.filename,
                        status: 'error',
                        message: `Failed to process file: ${error.message}`,
                    });
                }
            }

            // After OCR completion, trigger assignment for successful phone numbers
            const successfulPhoneNumbers = results
                .filter(r => r.status === 'success' && r.phone_number)
                .map(r => r.phone_number);

            let assignmentResults = [];

            if (successfulPhoneNumbers.length > 0) {
                try {
                    // Call assign phone logic for successful OCR results
                    assignmentResults = await triggerPhoneAssignment(
                        successfulPhoneNumbers,
                        storage_code,
                        uid,
                        taskId
                    );
                } catch (error) {
                    console.error('Failed to trigger phone assignment:', error);
                    // Add assignment errors to results
                    for (const phoneNumber of successfulPhoneNumbers) {
                        assignmentResults.push({
                            phone_number: phoneNumber,
                            status: 'error',
                            message: `Assignment failed: ${error.message}`
                        });
                    }
                }
            }

            // Add error items from OCR as "not found phone number" errors
            const ocrErrors = results
                .filter(r => r.status === 'error')
                .map(r => ({
                    phone_number: r.filename || 'unknown',
                    status: 'error',
                    message: `OCR failed: ${r.message}`
                }));

            // Combine OCR results and assignment results
            const finalResults = [
                ...results, // Original OCR results
                ...assignmentResults, // Assignment results
                ...ocrErrors // OCR errors as assignment errors
            ];

            // Update task with combined results
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: finalResults.every((r) => r.status === 'success') ? 'success' : 'error',
                    result: finalResults,
                    message: 'Phone upload and assignment processing completed',
                },
            });

            return finalResults;
        } catch (error) {
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: 'error',
                    message: error.message,
                },
            });
            throw error;
        }
    }, { connection: redis });

    // Handle worker events
    worker.on('completed', (job) => {
        console.log(`Upload job ${job.id} completed successfully`);
    });

    worker.on('failed', (job, err) => {
        console.error(`Upload job ${job.id} failed:`, err.message);
    });

    worker.on('error', (err) => {
        console.error('Upload Worker error:', err);
    });

    console.log('Upload Phone Worker started and listening for jobs...');
    return worker;
};

// Export the function for use in other modules
module.exports = createUploadPhoneWorker;

// If this file is run directly, start the worker
if (require.main === module) {
    createUploadPhoneWorker();
}
