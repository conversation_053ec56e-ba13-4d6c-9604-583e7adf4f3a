const { Worker } = require('bullmq');
const { prisma, redis } = require('../shared/instances');
const { gemini_ocr } = require('../services/ocr');

const createUploadPhoneWorker = () => {
    const worker = new Worker('upload_phone', async (job) => {
        const { taskId, files, storage_code, uid } = job.data;

        try {
            // Fetch storage
            const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
            if (!storage) {
                throw new Error('Storage not found');
            }

            // Authenticate user
            const user = await prisma.user.findFirst({
                where: { storageId: storage.id, uid, status: 'active' },
            });

            if (!user) {
                throw new Error('Unauthorized user');
            }

            const results = [];
            
            // Process each uploaded file
            for (const file of files) {
                try {
                    // Use OCR to extract phone numbers from the image
                    const ocrResults = await gemini_ocr(file.buffer);
                    
                    for (const result of ocrResults) {
                        results.push({
                            phone_number: result.phone_number,
                            serial: result.serial,
                            status: 'success',
                            message: 'Phone number extracted successfully',
                        });
                    }
                } catch (error) {
                    results.push({
                        filename: file.filename,
                        status: 'error',
                        message: `Failed to process file: ${error.message}`,
                    });
                }
            }

            // Update task
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: results.every((r) => r.status === 'success') ? 'success' : 'error',
                    result: results,
                    message: 'Phone upload processing completed',
                },
            });

            return results;
        } catch (error) {
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: 'error',
                    message: error.message,
                },
            });
            throw error;
        }
    }, { connection: redis });

    // Handle worker events
    worker.on('completed', (job) => {
        console.log(`Upload job ${job.id} completed successfully`);
    });

    worker.on('failed', (job, err) => {
        console.error(`Upload job ${job.id} failed:`, err.message);
    });

    worker.on('error', (err) => {
        console.error('Upload Worker error:', err);
    });

    console.log('Upload Phone Worker started and listening for jobs...');
    return worker;
};

// Export the function for use in other modules
module.exports = createUploadPhoneWorker;

// If this file is run directly, start the worker
if (require.main === module) {
    createUploadPhoneWorker();
}
