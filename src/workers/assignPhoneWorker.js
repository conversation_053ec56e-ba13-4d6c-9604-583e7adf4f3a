const { Worker } = require('bullmq');
const { prisma, redis } = require('../shared/instances');
const PuppeteerHelper = require('../services/puppeteerHelper');
const { getCachedPhoneItems, cachePhoneItems } = require('../cache/redis');

const createAssignPhoneWorker = () => {
    const worker = new Worker('assign_phone', async (job) => {
        const { taskId, productOrderId, phoneNumbers, storage_code, uid } = job.data;

        try {
            // Fetch storage
            const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
            if (!storage) {
                throw new Error('Storage not found');
            }

            // Initialize PuppeteerHelper
            const puppeteer = PuppeteerHelper.getInstance(storage.username, storage.password, storage.otp);
            await puppeteer.init();

            // Fetch product order
            const productOrder = await prisma.productOrder.findUnique({
                where: { id: productOrderId },
                include: { phoneItems: true },
            });

            if (!productOrder) {
                throw new Error('Product order not found');
            }

            // Cache phone items
            await cachePhoneItems(productOrderId, productOrder.phoneItems);

            const results = [];
            for (const phoneNumber of phoneNumbers) {
                let phoneItem = productOrder.phoneItems.find(
                    (pi) => pi.phoneNumber === phoneNumber && pi.status === 2
                );

                if (!phoneItem) {
                    results.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: 'Phone not found or already assigned',
                    });
                    continue;
                }

                if (productOrder.status !== 2) {
                    results.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: 'Product order status is not assignable',
                    });
                    continue;
                }

                try {
                    const orderInfo = await prisma.productOrder.findUnique({
                        where: { id: productOrderId },
                        select: { orderInfoId: true, tariffId: true },
                    });

                    await puppeteer.assign_phone(
                        phoneNumber,
                        orderInfo.tariffId,
                        phoneItem.id,
                        orderInfo.orderInfoId
                    );

                    await prisma.assignLog.create({
                        data: {
                            phoneNumber,
                            status: 'success',
                            message: 'Phone assigned successfully',
                            productOrderId,
                            userId: (await prisma.user.findFirst({ where: { uid } })).id,
                        },
                    });

                    results.push({
                        phone_number: phoneNumber,
                        status: 'success',
                        message: 'Phone assigned successfully',
                    });
                } catch (error) {
                    await prisma.assignLog.create({
                        data: {
                            phoneNumber,
                            status: 'error',
                            message: error.message,
                            productOrderId,
                            userId: (await prisma.user.findFirst({ where: { uid } })).id,
                        },
                    });

                    results.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: error.message,
                    });
                }
            }

            await puppeteer.close();

            // Update task
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: results.every((r) => r.status === 'success') ? 'success' : 'error',
                    result: results,
                    message: 'Phone assignment completed',
                },
            });

            return results;
        } catch (error) {
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: 'error',
                    message: error.message,
                },
            });
            throw error;
        }
    }, { connection: redis });

    // Handle worker events
    worker.on('completed', (job) => {
        console.log(`Job ${job.id} completed successfully`);
    });

    worker.on('failed', (job, err) => {
        console.error(`Job ${job.id} failed:`, err.message);
    });

    worker.on('error', (err) => {
        console.error('Worker error:', err);
    });

    console.log('Assign Phone Worker started and listening for jobs...');
    return worker;
};

// Export the function for use in other modules
module.exports = createAssignPhoneWorker;

// If this file is run directly, start the worker
if (require.main === module) {
    createAssignPhoneWorker();
}
