const { Worker } = require('bullmq');
const { prisma, redis } = require('../shared/instances');
const PuppeteerHelper = require('../services/puppeteerHelper');
const { getCachedPhoneItems, cachePhoneItems } = require('../cache/redis');

/**
 * AssignPhoneWorker - Handles phone assignment tasks
 *
 * Input: taskId, productOrderId, phoneNumbers, storage_code, uid
 *
 * Logic:
 * 1. Validates storage/user
 * 2. Gets cached phone items for the product order
 * 3. For each phone number in the group:
 *    - Finds phone items with status=2 and matching phoneNumber
 *    - For each found item:
 *      - Skips if product_order.status=2 (already filtered in API, but double-check)
 *      - Calls assign_phone API
 *      - Logs results in AssignLog (success or error)
 * 4. Updates Task with results
 *
 * Note: The API already filtered and built phones_to_assign list, so worker processes pre-filtered data
 */

const createAssignPhoneWorker = () => {
    const worker = new Worker('assign_phone', async (job) => {
        const { taskId, productOrderId, phoneNumbers, storage_code, uid } = job.data;

        try {
            // Fetch storage
            const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
            if (!storage) {
                throw new Error('Storage not found');
            }

            // Initialize PuppeteerHelper
            const puppeteer = PuppeteerHelper.getInstance(storage.username, storage.password, storage.otp);
            await puppeteer.init();

            // OPTIMIZED CACHING STRATEGY:
            // 1. Try to get phone items from cache first (major performance improvement)
            let phoneItems = await getCachedPhoneItems(productOrderId);
            let productOrder = null;

            if (!phoneItems) {
                // Cache miss - fetch from database
                productOrder = await prisma.productOrder.findUnique({
                    where: { id: productOrderId },
                    include: { phoneItems: true },
                });

                if (!productOrder) {
                    throw new Error('Product order not found');
                }

                phoneItems = productOrder.phoneItems;
                // Cache the phone items for future requests
                await cachePhoneItems(productOrderId, phoneItems);
            } else {
                // Cache hit - only fetch product order metadata (without phone items)
                productOrder = await prisma.productOrder.findUnique({
                    where: { id: productOrderId },
                    select: { id: true, name: true, status: true, tariffId: true, orderInfoId: true, tariffName: true, pckCode: true, orderDate: true },
                });

                if (!productOrder) {
                    throw new Error('Product order not found');
                }
            }

            // Cache user lookup to avoid repeated database queries
            const user = await prisma.user.findFirst({ where: { uid } });
            if (!user) {
                throw new Error('User not found');
            }

            const results = [];

            // WORKER LOGIC:
            // Process phones_to_assign that were already filtered by the API
            // The API already:
            // 1. Filtered phone_items with status = 2
            // 2. Skipped items where product_order.status = 2
            // 3. Built the phones_to_assign list
            // So worker just needs to process the pre-filtered phone numbers

            for (const phoneNumber of phoneNumbers) {
                // Find phone items with status = 2 and matching phoneNumber
                const matchingPhoneItems = phoneItems.filter(
                    (pi) => pi.phoneNumber === phoneNumber && pi.status === 2
                );

                if (matchingPhoneItems.length === 0) {
                    const result = {
                        phone_number: phoneNumber,
                        status: 'error',
                        message: 'Phone not found with status = 2',
                    };
                    results.push(result);

                    await prisma.assignLog.create({
                        data: {
                            phoneNumber,
                            status: 'error',
                            message: 'Phone not found with status = 2',
                            productOrderId,
                            userId: user.id,
                        },
                    });
                    continue;
                }

                // Process each found item (should already be filtered by API)
                for (const phoneItem of matchingPhoneItems) {
                    // Double-check: Skip if product_order.status = 2 (should already be filtered)
                    if (productOrder.status === 2) {
                        continue;
                    }

                    // Step 5: If not => call assign_phone api
                    try {
                        await puppeteer.assign_phone(
                            phoneNumber,
                            productOrder.tariffId,
                            phoneItem.id, // This is the orderDetailId
                            productOrder.orderInfoId
                        );

                        // If success => log to assign_log
                        await prisma.assignLog.create({
                            data: {
                                phoneNumber,
                                status: 'success',
                                message: 'Phone assigned successfully',
                                productOrderId,
                                userId: user.id,
                            },
                        });

                        results.push({
                            phone_number: phoneNumber,
                            status: 'success',
                            message: 'Phone assigned successfully',
                        });
                    } catch (error) {
                        // If error => log to assign_log
                        await prisma.assignLog.create({
                            data: {
                                phoneNumber,
                                status: 'error',
                                message: error.message,
                                productOrderId,
                                userId: user.id,
                            },
                        });

                        results.push({
                            phone_number: phoneNumber,
                            status: 'error',
                            message: error.message,
                        });
                    }
                }
            }

            await puppeteer.close();

            // Update task
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: results.every((r) => r.status === 'success') ? 'success' : 'error',
                    result: results,
                    message: 'Phone assignment completed',
                },
            });

            return results;
        } catch (error) {
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: 'error',
                    message: error.message,
                },
            });
            throw error;
        }
    }, { connection: redis });

    // Handle worker events
    worker.on('completed', (job) => {
        console.log(`Job ${job.id} completed successfully`);
    });

    worker.on('failed', (job, err) => {
        console.error(`Job ${job.id} failed:`, err.message);
    });

    worker.on('error', (err) => {
        console.error('Worker error:', err);
    });

    console.log('Assign Phone Worker started and listening for jobs...');
    return worker;
};

// Export the function for use in other modules
module.exports = createAssignPhoneWorker;

// If this file is run directly, start the worker
if (require.main === module) {
    createAssignPhoneWorker();
}
