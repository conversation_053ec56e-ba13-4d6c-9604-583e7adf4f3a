const { Worker } = require('bullmq');
const { prisma, redis } = require('../shared/instances');
const PuppeteerHelper = require('../services/puppeteerHelper');
const { getCachedPhoneItems, cachePhoneItems } = require('../cache/redis');

const createAssignPhoneWorker = () => {
    const worker = new Worker('assign_phone', async (job) => {
        const { taskId, productOrderId, phoneNumbers, storage_code, uid } = job.data;

        try {
            // Fetch storage
            const storage = await prisma.storage.findUnique({ where: { code: storage_code } });
            if (!storage) {
                throw new Error('Storage not found');
            }

            // Initialize PuppeteerHelper
            const puppeteer = PuppeteerHelper.getInstance(storage.username, storage.password, storage.otp);
            await puppeteer.init();

            // OPTIMIZED CACHING STRATEGY:
            // 1. Try to get phone items from cache first (major performance improvement)
            let phoneItems = await getCachedPhoneItems(productOrderId);
            let productOrder = null;

            if (!phoneItems) {
                // Cache miss - fetch from database
                productOrder = await prisma.productOrder.findUnique({
                    where: { id: productOrderId },
                    include: { phoneItems: true },
                });

                if (!productOrder) {
                    throw new Error('Product order not found');
                }

                phoneItems = productOrder.phoneItems;
                // Cache the phone items for future requests
                await cachePhoneItems(productOrderId, phoneItems);
            } else {
                // Cache hit - only fetch product order metadata (without phone items)
                productOrder = await prisma.productOrder.findUnique({
                    where: { id: productOrderId },
                    select: { id: true, name: true, status: true, tariffId: true, orderInfoId: true, tariffName: true, pckCode: true, orderDate: true },
                });

                if (!productOrder) {
                    throw new Error('Product order not found');
                }
            }

            // Cache user lookup to avoid repeated database queries
            const user = await prisma.user.findFirst({ where: { uid } });
            if (!user) {
                throw new Error('User not found');
            }

            const results = [];
            for (const phoneNumber of phoneNumbers) {
                let phoneItem = phoneItems.find(
                    (pi) => pi.phoneNumber === phoneNumber && pi.status === 2
                );

                if (!phoneItem) {
                    results.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: 'Phone not found or already assigned',
                    });
                    continue;
                }

                if (productOrder.status !== 2) {
                    results.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: 'Product order status is not assignable',
                    });
                    continue;
                }

                try {
                    // Use already fetched product order data instead of querying again
                    await puppeteer.assign_phone(
                        phoneNumber,
                        productOrder.tariffId,
                        phoneItem.id,
                        productOrder.orderInfoId
                    );

                    await prisma.assignLog.create({
                        data: {
                            phoneNumber,
                            status: 'success',
                            message: 'Phone assigned successfully',
                            productOrderId,
                            userId: user.id,
                        },
                    });

                    results.push({
                        phone_number: phoneNumber,
                        status: 'success',
                        message: 'Phone assigned successfully',
                    });
                } catch (error) {
                    await prisma.assignLog.create({
                        data: {
                            phoneNumber,
                            status: 'error',
                            message: error.message,
                            productOrderId,
                            userId: user.id,
                        },
                    });

                    results.push({
                        phone_number: phoneNumber,
                        status: 'error',
                        message: error.message,
                    });
                }
            }

            await puppeteer.close();

            // Update task
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: results.every((r) => r.status === 'success') ? 'success' : 'error',
                    result: results,
                    message: 'Phone assignment completed',
                },
            });

            return results;
        } catch (error) {
            await prisma.task.update({
                where: { id: taskId },
                data: {
                    status: 'error',
                    message: error.message,
                },
            });
            throw error;
        }
    }, { connection: redis });

    // Handle worker events
    worker.on('completed', (job) => {
        console.log(`Job ${job.id} completed successfully`);
    });

    worker.on('failed', (job, err) => {
        console.error(`Job ${job.id} failed:`, err.message);
    });

    worker.on('error', (err) => {
        console.error('Worker error:', err);
    });

    console.log('Assign Phone Worker started and listening for jobs...');
    return worker;
};

// Export the function for use in other modules
module.exports = createAssignPhoneWorker;

// If this file is run directly, start the worker
if (require.main === module) {
    createAssignPhoneWorker();
}
