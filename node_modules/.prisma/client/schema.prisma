// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Storage {
  id            Int            @id @default(autoincrement())
  username      String
  password      String
  otp           String
  code          String         @unique
  status        String         @default("active") // active, error
  message       String?
  users         User[]
  productOrders ProductOrder[]
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
}

model ProductOrder {
  id          String      @id
  name        String
  status      Int
  tariffId    String
  orderInfoId String
  tariffName  String
  pckCode     String
  orderDate   DateTime
  storage     Storage     @relation(fields: [storageId], references: [id])
  storageId   Int
  phoneItems  PhoneItem[]
  assignLogs  AssignLog[]
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
}

model PhoneItem {
  id             String       @id
  phoneNumber    String
  status         Int
  addedDate      DateTime?
  tariffCode     String?
  productOrder   ProductOrder @relation(fields: [productOrderId], references: [id])
  productOrderId String
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
}

model AssignLog {
  id             Int          @id @default(autoincrement())
  phoneNumber    String
  status         String // success, error
  message        String?
  productOrder   ProductOrder @relation(fields: [productOrderId], references: [id])
  productOrderId String
  user           User         @relation(fields: [userId], references: [id])
  userId         Int
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
}

model User {
  id          Int         @id @default(autoincrement())
  phoneNumber String
  uid         String?
  name        String?
  status      String      @default("not_active") // not_active, active
  storage     Storage     @relation(fields: [storageId], references: [id])
  storageId   Int
  assignLogs  AssignLog[]
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  @@unique([phoneNumber, storageId])
}

model Task {
  id        String   @id @default(uuid())
  type      String // assign_phone, upload_phone, scan_photos
  status    String   @default("pending") // pending, success, error
  data      Json // Task-specific data (e.g., phone_numbers, storage_code)
  result    Json? // Task result (e.g., phone assignment outcomes)
  message   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
