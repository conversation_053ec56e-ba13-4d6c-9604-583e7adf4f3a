
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model Storage
 * 
 */
export type Storage = $Result.DefaultSelection<Prisma.$StoragePayload>
/**
 * Model ProductOrder
 * 
 */
export type ProductOrder = $Result.DefaultSelection<Prisma.$ProductOrderPayload>
/**
 * Model PhoneItem
 * 
 */
export type PhoneItem = $Result.DefaultSelection<Prisma.$PhoneItemPayload>
/**
 * Model AssignLog
 * 
 */
export type AssignLog = $Result.DefaultSelection<Prisma.$AssignLogPayload>
/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model Task
 * 
 */
export type Task = $Result.DefaultSelection<Prisma.$TaskPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Storages
 * const storages = await prisma.storage.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Storages
   * const storages = await prisma.storage.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.storage`: Exposes CRUD operations for the **Storage** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Storages
    * const storages = await prisma.storage.findMany()
    * ```
    */
  get storage(): Prisma.StorageDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.productOrder`: Exposes CRUD operations for the **ProductOrder** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more ProductOrders
    * const productOrders = await prisma.productOrder.findMany()
    * ```
    */
  get productOrder(): Prisma.ProductOrderDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.phoneItem`: Exposes CRUD operations for the **PhoneItem** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more PhoneItems
    * const phoneItems = await prisma.phoneItem.findMany()
    * ```
    */
  get phoneItem(): Prisma.PhoneItemDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.assignLog`: Exposes CRUD operations for the **AssignLog** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more AssignLogs
    * const assignLogs = await prisma.assignLog.findMany()
    * ```
    */
  get assignLog(): Prisma.AssignLogDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs, ClientOptions>;

  /**
   * `prisma.task`: Exposes CRUD operations for the **Task** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Tasks
    * const tasks = await prisma.task.findMany()
    * ```
    */
  get task(): Prisma.TaskDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.9.0
   * Query Engine version: 81e4af48011447c3cc503a190e86995b66d2a28e
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    Storage: 'Storage',
    ProductOrder: 'ProductOrder',
    PhoneItem: 'PhoneItem',
    AssignLog: 'AssignLog',
    User: 'User',
    Task: 'Task'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "storage" | "productOrder" | "phoneItem" | "assignLog" | "user" | "task"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      Storage: {
        payload: Prisma.$StoragePayload<ExtArgs>
        fields: Prisma.StorageFieldRefs
        operations: {
          findUnique: {
            args: Prisma.StorageFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.StorageFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>
          }
          findFirst: {
            args: Prisma.StorageFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.StorageFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>
          }
          findMany: {
            args: Prisma.StorageFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>[]
          }
          create: {
            args: Prisma.StorageCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>
          }
          createMany: {
            args: Prisma.StorageCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.StorageCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>[]
          }
          delete: {
            args: Prisma.StorageDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>
          }
          update: {
            args: Prisma.StorageUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>
          }
          deleteMany: {
            args: Prisma.StorageDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.StorageUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.StorageUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>[]
          }
          upsert: {
            args: Prisma.StorageUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$StoragePayload>
          }
          aggregate: {
            args: Prisma.StorageAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateStorage>
          }
          groupBy: {
            args: Prisma.StorageGroupByArgs<ExtArgs>
            result: $Utils.Optional<StorageGroupByOutputType>[]
          }
          count: {
            args: Prisma.StorageCountArgs<ExtArgs>
            result: $Utils.Optional<StorageCountAggregateOutputType> | number
          }
        }
      }
      ProductOrder: {
        payload: Prisma.$ProductOrderPayload<ExtArgs>
        fields: Prisma.ProductOrderFieldRefs
        operations: {
          findUnique: {
            args: Prisma.ProductOrderFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.ProductOrderFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>
          }
          findFirst: {
            args: Prisma.ProductOrderFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.ProductOrderFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>
          }
          findMany: {
            args: Prisma.ProductOrderFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>[]
          }
          create: {
            args: Prisma.ProductOrderCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>
          }
          createMany: {
            args: Prisma.ProductOrderCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.ProductOrderCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>[]
          }
          delete: {
            args: Prisma.ProductOrderDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>
          }
          update: {
            args: Prisma.ProductOrderUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>
          }
          deleteMany: {
            args: Prisma.ProductOrderDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.ProductOrderUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.ProductOrderUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>[]
          }
          upsert: {
            args: Prisma.ProductOrderUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$ProductOrderPayload>
          }
          aggregate: {
            args: Prisma.ProductOrderAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateProductOrder>
          }
          groupBy: {
            args: Prisma.ProductOrderGroupByArgs<ExtArgs>
            result: $Utils.Optional<ProductOrderGroupByOutputType>[]
          }
          count: {
            args: Prisma.ProductOrderCountArgs<ExtArgs>
            result: $Utils.Optional<ProductOrderCountAggregateOutputType> | number
          }
        }
      }
      PhoneItem: {
        payload: Prisma.$PhoneItemPayload<ExtArgs>
        fields: Prisma.PhoneItemFieldRefs
        operations: {
          findUnique: {
            args: Prisma.PhoneItemFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.PhoneItemFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>
          }
          findFirst: {
            args: Prisma.PhoneItemFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.PhoneItemFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>
          }
          findMany: {
            args: Prisma.PhoneItemFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>[]
          }
          create: {
            args: Prisma.PhoneItemCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>
          }
          createMany: {
            args: Prisma.PhoneItemCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.PhoneItemCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>[]
          }
          delete: {
            args: Prisma.PhoneItemDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>
          }
          update: {
            args: Prisma.PhoneItemUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>
          }
          deleteMany: {
            args: Prisma.PhoneItemDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.PhoneItemUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.PhoneItemUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>[]
          }
          upsert: {
            args: Prisma.PhoneItemUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PhoneItemPayload>
          }
          aggregate: {
            args: Prisma.PhoneItemAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregatePhoneItem>
          }
          groupBy: {
            args: Prisma.PhoneItemGroupByArgs<ExtArgs>
            result: $Utils.Optional<PhoneItemGroupByOutputType>[]
          }
          count: {
            args: Prisma.PhoneItemCountArgs<ExtArgs>
            result: $Utils.Optional<PhoneItemCountAggregateOutputType> | number
          }
        }
      }
      AssignLog: {
        payload: Prisma.$AssignLogPayload<ExtArgs>
        fields: Prisma.AssignLogFieldRefs
        operations: {
          findUnique: {
            args: Prisma.AssignLogFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.AssignLogFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>
          }
          findFirst: {
            args: Prisma.AssignLogFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.AssignLogFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>
          }
          findMany: {
            args: Prisma.AssignLogFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>[]
          }
          create: {
            args: Prisma.AssignLogCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>
          }
          createMany: {
            args: Prisma.AssignLogCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.AssignLogCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>[]
          }
          delete: {
            args: Prisma.AssignLogDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>
          }
          update: {
            args: Prisma.AssignLogUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>
          }
          deleteMany: {
            args: Prisma.AssignLogDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.AssignLogUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.AssignLogUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>[]
          }
          upsert: {
            args: Prisma.AssignLogUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$AssignLogPayload>
          }
          aggregate: {
            args: Prisma.AssignLogAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateAssignLog>
          }
          groupBy: {
            args: Prisma.AssignLogGroupByArgs<ExtArgs>
            result: $Utils.Optional<AssignLogGroupByOutputType>[]
          }
          count: {
            args: Prisma.AssignLogCountArgs<ExtArgs>
            result: $Utils.Optional<AssignLogCountAggregateOutputType> | number
          }
        }
      }
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.UserUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      Task: {
        payload: Prisma.$TaskPayload<ExtArgs>
        fields: Prisma.TaskFieldRefs
        operations: {
          findUnique: {
            args: Prisma.TaskFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.TaskFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>
          }
          findFirst: {
            args: Prisma.TaskFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.TaskFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>
          }
          findMany: {
            args: Prisma.TaskFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>[]
          }
          create: {
            args: Prisma.TaskCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>
          }
          createMany: {
            args: Prisma.TaskCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.TaskCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>[]
          }
          delete: {
            args: Prisma.TaskDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>
          }
          update: {
            args: Prisma.TaskUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>
          }
          deleteMany: {
            args: Prisma.TaskDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.TaskUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.TaskUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>[]
          }
          upsert: {
            args: Prisma.TaskUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$TaskPayload>
          }
          aggregate: {
            args: Prisma.TaskAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateTask>
          }
          groupBy: {
            args: Prisma.TaskGroupByArgs<ExtArgs>
            result: $Utils.Optional<TaskGroupByOutputType>[]
          }
          count: {
            args: Prisma.TaskCountArgs<ExtArgs>
            result: $Utils.Optional<TaskCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    storage?: StorageOmit
    productOrder?: ProductOrderOmit
    phoneItem?: PhoneItemOmit
    assignLog?: AssignLogOmit
    user?: UserOmit
    task?: TaskOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type StorageCountOutputType
   */

  export type StorageCountOutputType = {
    users: number
    productOrders: number
  }

  export type StorageCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    users?: boolean | StorageCountOutputTypeCountUsersArgs
    productOrders?: boolean | StorageCountOutputTypeCountProductOrdersArgs
  }

  // Custom InputTypes
  /**
   * StorageCountOutputType without action
   */
  export type StorageCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the StorageCountOutputType
     */
    select?: StorageCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * StorageCountOutputType without action
   */
  export type StorageCountOutputTypeCountUsersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
  }

  /**
   * StorageCountOutputType without action
   */
  export type StorageCountOutputTypeCountProductOrdersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ProductOrderWhereInput
  }


  /**
   * Count Type ProductOrderCountOutputType
   */

  export type ProductOrderCountOutputType = {
    phoneItems: number
    assignLogs: number
  }

  export type ProductOrderCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    phoneItems?: boolean | ProductOrderCountOutputTypeCountPhoneItemsArgs
    assignLogs?: boolean | ProductOrderCountOutputTypeCountAssignLogsArgs
  }

  // Custom InputTypes
  /**
   * ProductOrderCountOutputType without action
   */
  export type ProductOrderCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrderCountOutputType
     */
    select?: ProductOrderCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * ProductOrderCountOutputType without action
   */
  export type ProductOrderCountOutputTypeCountPhoneItemsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PhoneItemWhereInput
  }

  /**
   * ProductOrderCountOutputType without action
   */
  export type ProductOrderCountOutputTypeCountAssignLogsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AssignLogWhereInput
  }


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    assignLogs: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    assignLogs?: boolean | UserCountOutputTypeCountAssignLogsArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountAssignLogsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AssignLogWhereInput
  }


  /**
   * Models
   */

  /**
   * Model Storage
   */

  export type AggregateStorage = {
    _count: StorageCountAggregateOutputType | null
    _avg: StorageAvgAggregateOutputType | null
    _sum: StorageSumAggregateOutputType | null
    _min: StorageMinAggregateOutputType | null
    _max: StorageMaxAggregateOutputType | null
  }

  export type StorageAvgAggregateOutputType = {
    id: number | null
  }

  export type StorageSumAggregateOutputType = {
    id: number | null
  }

  export type StorageMinAggregateOutputType = {
    id: number | null
    username: string | null
    password: string | null
    otp: string | null
    code: string | null
    status: string | null
    message: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StorageMaxAggregateOutputType = {
    id: number | null
    username: string | null
    password: string | null
    otp: string | null
    code: string | null
    status: string | null
    message: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type StorageCountAggregateOutputType = {
    id: number
    username: number
    password: number
    otp: number
    code: number
    status: number
    message: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type StorageAvgAggregateInputType = {
    id?: true
  }

  export type StorageSumAggregateInputType = {
    id?: true
  }

  export type StorageMinAggregateInputType = {
    id?: true
    username?: true
    password?: true
    otp?: true
    code?: true
    status?: true
    message?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StorageMaxAggregateInputType = {
    id?: true
    username?: true
    password?: true
    otp?: true
    code?: true
    status?: true
    message?: true
    createdAt?: true
    updatedAt?: true
  }

  export type StorageCountAggregateInputType = {
    id?: true
    username?: true
    password?: true
    otp?: true
    code?: true
    status?: true
    message?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type StorageAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Storage to aggregate.
     */
    where?: StorageWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Storages to fetch.
     */
    orderBy?: StorageOrderByWithRelationInput | StorageOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: StorageWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Storages from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Storages.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Storages
    **/
    _count?: true | StorageCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: StorageAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: StorageSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: StorageMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: StorageMaxAggregateInputType
  }

  export type GetStorageAggregateType<T extends StorageAggregateArgs> = {
        [P in keyof T & keyof AggregateStorage]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateStorage[P]>
      : GetScalarType<T[P], AggregateStorage[P]>
  }




  export type StorageGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: StorageWhereInput
    orderBy?: StorageOrderByWithAggregationInput | StorageOrderByWithAggregationInput[]
    by: StorageScalarFieldEnum[] | StorageScalarFieldEnum
    having?: StorageScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: StorageCountAggregateInputType | true
    _avg?: StorageAvgAggregateInputType
    _sum?: StorageSumAggregateInputType
    _min?: StorageMinAggregateInputType
    _max?: StorageMaxAggregateInputType
  }

  export type StorageGroupByOutputType = {
    id: number
    username: string
    password: string
    otp: string
    code: string
    status: string
    message: string | null
    createdAt: Date
    updatedAt: Date
    _count: StorageCountAggregateOutputType | null
    _avg: StorageAvgAggregateOutputType | null
    _sum: StorageSumAggregateOutputType | null
    _min: StorageMinAggregateOutputType | null
    _max: StorageMaxAggregateOutputType | null
  }

  type GetStorageGroupByPayload<T extends StorageGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<StorageGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof StorageGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], StorageGroupByOutputType[P]>
            : GetScalarType<T[P], StorageGroupByOutputType[P]>
        }
      >
    >


  export type StorageSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    password?: boolean
    otp?: boolean
    code?: boolean
    status?: boolean
    message?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    users?: boolean | Storage$usersArgs<ExtArgs>
    productOrders?: boolean | Storage$productOrdersArgs<ExtArgs>
    _count?: boolean | StorageCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["storage"]>

  export type StorageSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    password?: boolean
    otp?: boolean
    code?: boolean
    status?: boolean
    message?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["storage"]>

  export type StorageSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    username?: boolean
    password?: boolean
    otp?: boolean
    code?: boolean
    status?: boolean
    message?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["storage"]>

  export type StorageSelectScalar = {
    id?: boolean
    username?: boolean
    password?: boolean
    otp?: boolean
    code?: boolean
    status?: boolean
    message?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type StorageOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "username" | "password" | "otp" | "code" | "status" | "message" | "createdAt" | "updatedAt", ExtArgs["result"]["storage"]>
  export type StorageInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    users?: boolean | Storage$usersArgs<ExtArgs>
    productOrders?: boolean | Storage$productOrdersArgs<ExtArgs>
    _count?: boolean | StorageCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type StorageIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}
  export type StorageIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $StoragePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Storage"
    objects: {
      users: Prisma.$UserPayload<ExtArgs>[]
      productOrders: Prisma.$ProductOrderPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      username: string
      password: string
      otp: string
      code: string
      status: string
      message: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["storage"]>
    composites: {}
  }

  type StorageGetPayload<S extends boolean | null | undefined | StorageDefaultArgs> = $Result.GetResult<Prisma.$StoragePayload, S>

  type StorageCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<StorageFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: StorageCountAggregateInputType | true
    }

  export interface StorageDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Storage'], meta: { name: 'Storage' } }
    /**
     * Find zero or one Storage that matches the filter.
     * @param {StorageFindUniqueArgs} args - Arguments to find a Storage
     * @example
     * // Get one Storage
     * const storage = await prisma.storage.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends StorageFindUniqueArgs>(args: SelectSubset<T, StorageFindUniqueArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Storage that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {StorageFindUniqueOrThrowArgs} args - Arguments to find a Storage
     * @example
     * // Get one Storage
     * const storage = await prisma.storage.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends StorageFindUniqueOrThrowArgs>(args: SelectSubset<T, StorageFindUniqueOrThrowArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Storage that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StorageFindFirstArgs} args - Arguments to find a Storage
     * @example
     * // Get one Storage
     * const storage = await prisma.storage.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends StorageFindFirstArgs>(args?: SelectSubset<T, StorageFindFirstArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Storage that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StorageFindFirstOrThrowArgs} args - Arguments to find a Storage
     * @example
     * // Get one Storage
     * const storage = await prisma.storage.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends StorageFindFirstOrThrowArgs>(args?: SelectSubset<T, StorageFindFirstOrThrowArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Storages that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StorageFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Storages
     * const storages = await prisma.storage.findMany()
     * 
     * // Get first 10 Storages
     * const storages = await prisma.storage.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const storageWithIdOnly = await prisma.storage.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends StorageFindManyArgs>(args?: SelectSubset<T, StorageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Storage.
     * @param {StorageCreateArgs} args - Arguments to create a Storage.
     * @example
     * // Create one Storage
     * const Storage = await prisma.storage.create({
     *   data: {
     *     // ... data to create a Storage
     *   }
     * })
     * 
     */
    create<T extends StorageCreateArgs>(args: SelectSubset<T, StorageCreateArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Storages.
     * @param {StorageCreateManyArgs} args - Arguments to create many Storages.
     * @example
     * // Create many Storages
     * const storage = await prisma.storage.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends StorageCreateManyArgs>(args?: SelectSubset<T, StorageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Storages and returns the data saved in the database.
     * @param {StorageCreateManyAndReturnArgs} args - Arguments to create many Storages.
     * @example
     * // Create many Storages
     * const storage = await prisma.storage.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Storages and only return the `id`
     * const storageWithIdOnly = await prisma.storage.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends StorageCreateManyAndReturnArgs>(args?: SelectSubset<T, StorageCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Storage.
     * @param {StorageDeleteArgs} args - Arguments to delete one Storage.
     * @example
     * // Delete one Storage
     * const Storage = await prisma.storage.delete({
     *   where: {
     *     // ... filter to delete one Storage
     *   }
     * })
     * 
     */
    delete<T extends StorageDeleteArgs>(args: SelectSubset<T, StorageDeleteArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Storage.
     * @param {StorageUpdateArgs} args - Arguments to update one Storage.
     * @example
     * // Update one Storage
     * const storage = await prisma.storage.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends StorageUpdateArgs>(args: SelectSubset<T, StorageUpdateArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Storages.
     * @param {StorageDeleteManyArgs} args - Arguments to filter Storages to delete.
     * @example
     * // Delete a few Storages
     * const { count } = await prisma.storage.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends StorageDeleteManyArgs>(args?: SelectSubset<T, StorageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Storages.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StorageUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Storages
     * const storage = await prisma.storage.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends StorageUpdateManyArgs>(args: SelectSubset<T, StorageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Storages and returns the data updated in the database.
     * @param {StorageUpdateManyAndReturnArgs} args - Arguments to update many Storages.
     * @example
     * // Update many Storages
     * const storage = await prisma.storage.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Storages and only return the `id`
     * const storageWithIdOnly = await prisma.storage.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends StorageUpdateManyAndReturnArgs>(args: SelectSubset<T, StorageUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Storage.
     * @param {StorageUpsertArgs} args - Arguments to update or create a Storage.
     * @example
     * // Update or create a Storage
     * const storage = await prisma.storage.upsert({
     *   create: {
     *     // ... data to create a Storage
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Storage we want to update
     *   }
     * })
     */
    upsert<T extends StorageUpsertArgs>(args: SelectSubset<T, StorageUpsertArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Storages.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StorageCountArgs} args - Arguments to filter Storages to count.
     * @example
     * // Count the number of Storages
     * const count = await prisma.storage.count({
     *   where: {
     *     // ... the filter for the Storages we want to count
     *   }
     * })
    **/
    count<T extends StorageCountArgs>(
      args?: Subset<T, StorageCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], StorageCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Storage.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StorageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends StorageAggregateArgs>(args: Subset<T, StorageAggregateArgs>): Prisma.PrismaPromise<GetStorageAggregateType<T>>

    /**
     * Group by Storage.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {StorageGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends StorageGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: StorageGroupByArgs['orderBy'] }
        : { orderBy?: StorageGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, StorageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetStorageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Storage model
   */
  readonly fields: StorageFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Storage.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__StorageClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    users<T extends Storage$usersArgs<ExtArgs> = {}>(args?: Subset<T, Storage$usersArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    productOrders<T extends Storage$productOrdersArgs<ExtArgs> = {}>(args?: Subset<T, Storage$productOrdersArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Storage model
   */
  interface StorageFieldRefs {
    readonly id: FieldRef<"Storage", 'Int'>
    readonly username: FieldRef<"Storage", 'String'>
    readonly password: FieldRef<"Storage", 'String'>
    readonly otp: FieldRef<"Storage", 'String'>
    readonly code: FieldRef<"Storage", 'String'>
    readonly status: FieldRef<"Storage", 'String'>
    readonly message: FieldRef<"Storage", 'String'>
    readonly createdAt: FieldRef<"Storage", 'DateTime'>
    readonly updatedAt: FieldRef<"Storage", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Storage findUnique
   */
  export type StorageFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * Filter, which Storage to fetch.
     */
    where: StorageWhereUniqueInput
  }

  /**
   * Storage findUniqueOrThrow
   */
  export type StorageFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * Filter, which Storage to fetch.
     */
    where: StorageWhereUniqueInput
  }

  /**
   * Storage findFirst
   */
  export type StorageFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * Filter, which Storage to fetch.
     */
    where?: StorageWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Storages to fetch.
     */
    orderBy?: StorageOrderByWithRelationInput | StorageOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Storages.
     */
    cursor?: StorageWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Storages from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Storages.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Storages.
     */
    distinct?: StorageScalarFieldEnum | StorageScalarFieldEnum[]
  }

  /**
   * Storage findFirstOrThrow
   */
  export type StorageFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * Filter, which Storage to fetch.
     */
    where?: StorageWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Storages to fetch.
     */
    orderBy?: StorageOrderByWithRelationInput | StorageOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Storages.
     */
    cursor?: StorageWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Storages from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Storages.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Storages.
     */
    distinct?: StorageScalarFieldEnum | StorageScalarFieldEnum[]
  }

  /**
   * Storage findMany
   */
  export type StorageFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * Filter, which Storages to fetch.
     */
    where?: StorageWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Storages to fetch.
     */
    orderBy?: StorageOrderByWithRelationInput | StorageOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Storages.
     */
    cursor?: StorageWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Storages from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Storages.
     */
    skip?: number
    distinct?: StorageScalarFieldEnum | StorageScalarFieldEnum[]
  }

  /**
   * Storage create
   */
  export type StorageCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * The data needed to create a Storage.
     */
    data: XOR<StorageCreateInput, StorageUncheckedCreateInput>
  }

  /**
   * Storage createMany
   */
  export type StorageCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Storages.
     */
    data: StorageCreateManyInput | StorageCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Storage createManyAndReturn
   */
  export type StorageCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * The data used to create many Storages.
     */
    data: StorageCreateManyInput | StorageCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Storage update
   */
  export type StorageUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * The data needed to update a Storage.
     */
    data: XOR<StorageUpdateInput, StorageUncheckedUpdateInput>
    /**
     * Choose, which Storage to update.
     */
    where: StorageWhereUniqueInput
  }

  /**
   * Storage updateMany
   */
  export type StorageUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Storages.
     */
    data: XOR<StorageUpdateManyMutationInput, StorageUncheckedUpdateManyInput>
    /**
     * Filter which Storages to update
     */
    where?: StorageWhereInput
    /**
     * Limit how many Storages to update.
     */
    limit?: number
  }

  /**
   * Storage updateManyAndReturn
   */
  export type StorageUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * The data used to update Storages.
     */
    data: XOR<StorageUpdateManyMutationInput, StorageUncheckedUpdateManyInput>
    /**
     * Filter which Storages to update
     */
    where?: StorageWhereInput
    /**
     * Limit how many Storages to update.
     */
    limit?: number
  }

  /**
   * Storage upsert
   */
  export type StorageUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * The filter to search for the Storage to update in case it exists.
     */
    where: StorageWhereUniqueInput
    /**
     * In case the Storage found by the `where` argument doesn't exist, create a new Storage with this data.
     */
    create: XOR<StorageCreateInput, StorageUncheckedCreateInput>
    /**
     * In case the Storage was found with the provided `where` argument, update it with this data.
     */
    update: XOR<StorageUpdateInput, StorageUncheckedUpdateInput>
  }

  /**
   * Storage delete
   */
  export type StorageDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
    /**
     * Filter which Storage to delete.
     */
    where: StorageWhereUniqueInput
  }

  /**
   * Storage deleteMany
   */
  export type StorageDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Storages to delete
     */
    where?: StorageWhereInput
    /**
     * Limit how many Storages to delete.
     */
    limit?: number
  }

  /**
   * Storage.users
   */
  export type Storage$usersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    where?: UserWhereInput
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    cursor?: UserWhereUniqueInput
    take?: number
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * Storage.productOrders
   */
  export type Storage$productOrdersArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    where?: ProductOrderWhereInput
    orderBy?: ProductOrderOrderByWithRelationInput | ProductOrderOrderByWithRelationInput[]
    cursor?: ProductOrderWhereUniqueInput
    take?: number
    skip?: number
    distinct?: ProductOrderScalarFieldEnum | ProductOrderScalarFieldEnum[]
  }

  /**
   * Storage without action
   */
  export type StorageDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Storage
     */
    select?: StorageSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Storage
     */
    omit?: StorageOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: StorageInclude<ExtArgs> | null
  }


  /**
   * Model ProductOrder
   */

  export type AggregateProductOrder = {
    _count: ProductOrderCountAggregateOutputType | null
    _avg: ProductOrderAvgAggregateOutputType | null
    _sum: ProductOrderSumAggregateOutputType | null
    _min: ProductOrderMinAggregateOutputType | null
    _max: ProductOrderMaxAggregateOutputType | null
  }

  export type ProductOrderAvgAggregateOutputType = {
    status: number | null
    storageId: number | null
  }

  export type ProductOrderSumAggregateOutputType = {
    status: number | null
    storageId: number | null
  }

  export type ProductOrderMinAggregateOutputType = {
    id: string | null
    name: string | null
    status: number | null
    tariffId: string | null
    orderInfoId: string | null
    tariffName: string | null
    pckCode: string | null
    orderDate: Date | null
    storageId: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ProductOrderMaxAggregateOutputType = {
    id: string | null
    name: string | null
    status: number | null
    tariffId: string | null
    orderInfoId: string | null
    tariffName: string | null
    pckCode: string | null
    orderDate: Date | null
    storageId: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type ProductOrderCountAggregateOutputType = {
    id: number
    name: number
    status: number
    tariffId: number
    orderInfoId: number
    tariffName: number
    pckCode: number
    orderDate: number
    storageId: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type ProductOrderAvgAggregateInputType = {
    status?: true
    storageId?: true
  }

  export type ProductOrderSumAggregateInputType = {
    status?: true
    storageId?: true
  }

  export type ProductOrderMinAggregateInputType = {
    id?: true
    name?: true
    status?: true
    tariffId?: true
    orderInfoId?: true
    tariffName?: true
    pckCode?: true
    orderDate?: true
    storageId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ProductOrderMaxAggregateInputType = {
    id?: true
    name?: true
    status?: true
    tariffId?: true
    orderInfoId?: true
    tariffName?: true
    pckCode?: true
    orderDate?: true
    storageId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type ProductOrderCountAggregateInputType = {
    id?: true
    name?: true
    status?: true
    tariffId?: true
    orderInfoId?: true
    tariffName?: true
    pckCode?: true
    orderDate?: true
    storageId?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type ProductOrderAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ProductOrder to aggregate.
     */
    where?: ProductOrderWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ProductOrders to fetch.
     */
    orderBy?: ProductOrderOrderByWithRelationInput | ProductOrderOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: ProductOrderWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ProductOrders from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ProductOrders.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned ProductOrders
    **/
    _count?: true | ProductOrderCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: ProductOrderAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: ProductOrderSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: ProductOrderMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: ProductOrderMaxAggregateInputType
  }

  export type GetProductOrderAggregateType<T extends ProductOrderAggregateArgs> = {
        [P in keyof T & keyof AggregateProductOrder]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateProductOrder[P]>
      : GetScalarType<T[P], AggregateProductOrder[P]>
  }




  export type ProductOrderGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: ProductOrderWhereInput
    orderBy?: ProductOrderOrderByWithAggregationInput | ProductOrderOrderByWithAggregationInput[]
    by: ProductOrderScalarFieldEnum[] | ProductOrderScalarFieldEnum
    having?: ProductOrderScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: ProductOrderCountAggregateInputType | true
    _avg?: ProductOrderAvgAggregateInputType
    _sum?: ProductOrderSumAggregateInputType
    _min?: ProductOrderMinAggregateInputType
    _max?: ProductOrderMaxAggregateInputType
  }

  export type ProductOrderGroupByOutputType = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date
    storageId: number
    createdAt: Date
    updatedAt: Date
    _count: ProductOrderCountAggregateOutputType | null
    _avg: ProductOrderAvgAggregateOutputType | null
    _sum: ProductOrderSumAggregateOutputType | null
    _min: ProductOrderMinAggregateOutputType | null
    _max: ProductOrderMaxAggregateOutputType | null
  }

  type GetProductOrderGroupByPayload<T extends ProductOrderGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<ProductOrderGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof ProductOrderGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], ProductOrderGroupByOutputType[P]>
            : GetScalarType<T[P], ProductOrderGroupByOutputType[P]>
        }
      >
    >


  export type ProductOrderSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    status?: boolean
    tariffId?: boolean
    orderInfoId?: boolean
    tariffName?: boolean
    pckCode?: boolean
    orderDate?: boolean
    storageId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    storage?: boolean | StorageDefaultArgs<ExtArgs>
    phoneItems?: boolean | ProductOrder$phoneItemsArgs<ExtArgs>
    assignLogs?: boolean | ProductOrder$assignLogsArgs<ExtArgs>
    _count?: boolean | ProductOrderCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["productOrder"]>

  export type ProductOrderSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    status?: boolean
    tariffId?: boolean
    orderInfoId?: boolean
    tariffName?: boolean
    pckCode?: boolean
    orderDate?: boolean
    storageId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    storage?: boolean | StorageDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["productOrder"]>

  export type ProductOrderSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    status?: boolean
    tariffId?: boolean
    orderInfoId?: boolean
    tariffName?: boolean
    pckCode?: boolean
    orderDate?: boolean
    storageId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    storage?: boolean | StorageDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["productOrder"]>

  export type ProductOrderSelectScalar = {
    id?: boolean
    name?: boolean
    status?: boolean
    tariffId?: boolean
    orderInfoId?: boolean
    tariffName?: boolean
    pckCode?: boolean
    orderDate?: boolean
    storageId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type ProductOrderOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "name" | "status" | "tariffId" | "orderInfoId" | "tariffName" | "pckCode" | "orderDate" | "storageId" | "createdAt" | "updatedAt", ExtArgs["result"]["productOrder"]>
  export type ProductOrderInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    storage?: boolean | StorageDefaultArgs<ExtArgs>
    phoneItems?: boolean | ProductOrder$phoneItemsArgs<ExtArgs>
    assignLogs?: boolean | ProductOrder$assignLogsArgs<ExtArgs>
    _count?: boolean | ProductOrderCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type ProductOrderIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    storage?: boolean | StorageDefaultArgs<ExtArgs>
  }
  export type ProductOrderIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    storage?: boolean | StorageDefaultArgs<ExtArgs>
  }

  export type $ProductOrderPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "ProductOrder"
    objects: {
      storage: Prisma.$StoragePayload<ExtArgs>
      phoneItems: Prisma.$PhoneItemPayload<ExtArgs>[]
      assignLogs: Prisma.$AssignLogPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      name: string
      status: number
      tariffId: string
      orderInfoId: string
      tariffName: string
      pckCode: string
      orderDate: Date
      storageId: number
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["productOrder"]>
    composites: {}
  }

  type ProductOrderGetPayload<S extends boolean | null | undefined | ProductOrderDefaultArgs> = $Result.GetResult<Prisma.$ProductOrderPayload, S>

  type ProductOrderCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<ProductOrderFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: ProductOrderCountAggregateInputType | true
    }

  export interface ProductOrderDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['ProductOrder'], meta: { name: 'ProductOrder' } }
    /**
     * Find zero or one ProductOrder that matches the filter.
     * @param {ProductOrderFindUniqueArgs} args - Arguments to find a ProductOrder
     * @example
     * // Get one ProductOrder
     * const productOrder = await prisma.productOrder.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends ProductOrderFindUniqueArgs>(args: SelectSubset<T, ProductOrderFindUniqueArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one ProductOrder that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {ProductOrderFindUniqueOrThrowArgs} args - Arguments to find a ProductOrder
     * @example
     * // Get one ProductOrder
     * const productOrder = await prisma.productOrder.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends ProductOrderFindUniqueOrThrowArgs>(args: SelectSubset<T, ProductOrderFindUniqueOrThrowArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ProductOrder that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductOrderFindFirstArgs} args - Arguments to find a ProductOrder
     * @example
     * // Get one ProductOrder
     * const productOrder = await prisma.productOrder.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends ProductOrderFindFirstArgs>(args?: SelectSubset<T, ProductOrderFindFirstArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first ProductOrder that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductOrderFindFirstOrThrowArgs} args - Arguments to find a ProductOrder
     * @example
     * // Get one ProductOrder
     * const productOrder = await prisma.productOrder.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends ProductOrderFindFirstOrThrowArgs>(args?: SelectSubset<T, ProductOrderFindFirstOrThrowArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more ProductOrders that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductOrderFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all ProductOrders
     * const productOrders = await prisma.productOrder.findMany()
     * 
     * // Get first 10 ProductOrders
     * const productOrders = await prisma.productOrder.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const productOrderWithIdOnly = await prisma.productOrder.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends ProductOrderFindManyArgs>(args?: SelectSubset<T, ProductOrderFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a ProductOrder.
     * @param {ProductOrderCreateArgs} args - Arguments to create a ProductOrder.
     * @example
     * // Create one ProductOrder
     * const ProductOrder = await prisma.productOrder.create({
     *   data: {
     *     // ... data to create a ProductOrder
     *   }
     * })
     * 
     */
    create<T extends ProductOrderCreateArgs>(args: SelectSubset<T, ProductOrderCreateArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many ProductOrders.
     * @param {ProductOrderCreateManyArgs} args - Arguments to create many ProductOrders.
     * @example
     * // Create many ProductOrders
     * const productOrder = await prisma.productOrder.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends ProductOrderCreateManyArgs>(args?: SelectSubset<T, ProductOrderCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many ProductOrders and returns the data saved in the database.
     * @param {ProductOrderCreateManyAndReturnArgs} args - Arguments to create many ProductOrders.
     * @example
     * // Create many ProductOrders
     * const productOrder = await prisma.productOrder.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many ProductOrders and only return the `id`
     * const productOrderWithIdOnly = await prisma.productOrder.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends ProductOrderCreateManyAndReturnArgs>(args?: SelectSubset<T, ProductOrderCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a ProductOrder.
     * @param {ProductOrderDeleteArgs} args - Arguments to delete one ProductOrder.
     * @example
     * // Delete one ProductOrder
     * const ProductOrder = await prisma.productOrder.delete({
     *   where: {
     *     // ... filter to delete one ProductOrder
     *   }
     * })
     * 
     */
    delete<T extends ProductOrderDeleteArgs>(args: SelectSubset<T, ProductOrderDeleteArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one ProductOrder.
     * @param {ProductOrderUpdateArgs} args - Arguments to update one ProductOrder.
     * @example
     * // Update one ProductOrder
     * const productOrder = await prisma.productOrder.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends ProductOrderUpdateArgs>(args: SelectSubset<T, ProductOrderUpdateArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more ProductOrders.
     * @param {ProductOrderDeleteManyArgs} args - Arguments to filter ProductOrders to delete.
     * @example
     * // Delete a few ProductOrders
     * const { count } = await prisma.productOrder.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends ProductOrderDeleteManyArgs>(args?: SelectSubset<T, ProductOrderDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ProductOrders.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductOrderUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many ProductOrders
     * const productOrder = await prisma.productOrder.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends ProductOrderUpdateManyArgs>(args: SelectSubset<T, ProductOrderUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more ProductOrders and returns the data updated in the database.
     * @param {ProductOrderUpdateManyAndReturnArgs} args - Arguments to update many ProductOrders.
     * @example
     * // Update many ProductOrders
     * const productOrder = await prisma.productOrder.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more ProductOrders and only return the `id`
     * const productOrderWithIdOnly = await prisma.productOrder.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends ProductOrderUpdateManyAndReturnArgs>(args: SelectSubset<T, ProductOrderUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one ProductOrder.
     * @param {ProductOrderUpsertArgs} args - Arguments to update or create a ProductOrder.
     * @example
     * // Update or create a ProductOrder
     * const productOrder = await prisma.productOrder.upsert({
     *   create: {
     *     // ... data to create a ProductOrder
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the ProductOrder we want to update
     *   }
     * })
     */
    upsert<T extends ProductOrderUpsertArgs>(args: SelectSubset<T, ProductOrderUpsertArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of ProductOrders.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductOrderCountArgs} args - Arguments to filter ProductOrders to count.
     * @example
     * // Count the number of ProductOrders
     * const count = await prisma.productOrder.count({
     *   where: {
     *     // ... the filter for the ProductOrders we want to count
     *   }
     * })
    **/
    count<T extends ProductOrderCountArgs>(
      args?: Subset<T, ProductOrderCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], ProductOrderCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a ProductOrder.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductOrderAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends ProductOrderAggregateArgs>(args: Subset<T, ProductOrderAggregateArgs>): Prisma.PrismaPromise<GetProductOrderAggregateType<T>>

    /**
     * Group by ProductOrder.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {ProductOrderGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends ProductOrderGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: ProductOrderGroupByArgs['orderBy'] }
        : { orderBy?: ProductOrderGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, ProductOrderGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetProductOrderGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the ProductOrder model
   */
  readonly fields: ProductOrderFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for ProductOrder.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__ProductOrderClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    storage<T extends StorageDefaultArgs<ExtArgs> = {}>(args?: Subset<T, StorageDefaultArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    phoneItems<T extends ProductOrder$phoneItemsArgs<ExtArgs> = {}>(args?: Subset<T, ProductOrder$phoneItemsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    assignLogs<T extends ProductOrder$assignLogsArgs<ExtArgs> = {}>(args?: Subset<T, ProductOrder$assignLogsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the ProductOrder model
   */
  interface ProductOrderFieldRefs {
    readonly id: FieldRef<"ProductOrder", 'String'>
    readonly name: FieldRef<"ProductOrder", 'String'>
    readonly status: FieldRef<"ProductOrder", 'Int'>
    readonly tariffId: FieldRef<"ProductOrder", 'String'>
    readonly orderInfoId: FieldRef<"ProductOrder", 'String'>
    readonly tariffName: FieldRef<"ProductOrder", 'String'>
    readonly pckCode: FieldRef<"ProductOrder", 'String'>
    readonly orderDate: FieldRef<"ProductOrder", 'DateTime'>
    readonly storageId: FieldRef<"ProductOrder", 'Int'>
    readonly createdAt: FieldRef<"ProductOrder", 'DateTime'>
    readonly updatedAt: FieldRef<"ProductOrder", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * ProductOrder findUnique
   */
  export type ProductOrderFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * Filter, which ProductOrder to fetch.
     */
    where: ProductOrderWhereUniqueInput
  }

  /**
   * ProductOrder findUniqueOrThrow
   */
  export type ProductOrderFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * Filter, which ProductOrder to fetch.
     */
    where: ProductOrderWhereUniqueInput
  }

  /**
   * ProductOrder findFirst
   */
  export type ProductOrderFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * Filter, which ProductOrder to fetch.
     */
    where?: ProductOrderWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ProductOrders to fetch.
     */
    orderBy?: ProductOrderOrderByWithRelationInput | ProductOrderOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ProductOrders.
     */
    cursor?: ProductOrderWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ProductOrders from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ProductOrders.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ProductOrders.
     */
    distinct?: ProductOrderScalarFieldEnum | ProductOrderScalarFieldEnum[]
  }

  /**
   * ProductOrder findFirstOrThrow
   */
  export type ProductOrderFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * Filter, which ProductOrder to fetch.
     */
    where?: ProductOrderWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ProductOrders to fetch.
     */
    orderBy?: ProductOrderOrderByWithRelationInput | ProductOrderOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for ProductOrders.
     */
    cursor?: ProductOrderWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ProductOrders from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ProductOrders.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of ProductOrders.
     */
    distinct?: ProductOrderScalarFieldEnum | ProductOrderScalarFieldEnum[]
  }

  /**
   * ProductOrder findMany
   */
  export type ProductOrderFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * Filter, which ProductOrders to fetch.
     */
    where?: ProductOrderWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of ProductOrders to fetch.
     */
    orderBy?: ProductOrderOrderByWithRelationInput | ProductOrderOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing ProductOrders.
     */
    cursor?: ProductOrderWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` ProductOrders from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` ProductOrders.
     */
    skip?: number
    distinct?: ProductOrderScalarFieldEnum | ProductOrderScalarFieldEnum[]
  }

  /**
   * ProductOrder create
   */
  export type ProductOrderCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * The data needed to create a ProductOrder.
     */
    data: XOR<ProductOrderCreateInput, ProductOrderUncheckedCreateInput>
  }

  /**
   * ProductOrder createMany
   */
  export type ProductOrderCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many ProductOrders.
     */
    data: ProductOrderCreateManyInput | ProductOrderCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * ProductOrder createManyAndReturn
   */
  export type ProductOrderCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * The data used to create many ProductOrders.
     */
    data: ProductOrderCreateManyInput | ProductOrderCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * ProductOrder update
   */
  export type ProductOrderUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * The data needed to update a ProductOrder.
     */
    data: XOR<ProductOrderUpdateInput, ProductOrderUncheckedUpdateInput>
    /**
     * Choose, which ProductOrder to update.
     */
    where: ProductOrderWhereUniqueInput
  }

  /**
   * ProductOrder updateMany
   */
  export type ProductOrderUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update ProductOrders.
     */
    data: XOR<ProductOrderUpdateManyMutationInput, ProductOrderUncheckedUpdateManyInput>
    /**
     * Filter which ProductOrders to update
     */
    where?: ProductOrderWhereInput
    /**
     * Limit how many ProductOrders to update.
     */
    limit?: number
  }

  /**
   * ProductOrder updateManyAndReturn
   */
  export type ProductOrderUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * The data used to update ProductOrders.
     */
    data: XOR<ProductOrderUpdateManyMutationInput, ProductOrderUncheckedUpdateManyInput>
    /**
     * Filter which ProductOrders to update
     */
    where?: ProductOrderWhereInput
    /**
     * Limit how many ProductOrders to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * ProductOrder upsert
   */
  export type ProductOrderUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * The filter to search for the ProductOrder to update in case it exists.
     */
    where: ProductOrderWhereUniqueInput
    /**
     * In case the ProductOrder found by the `where` argument doesn't exist, create a new ProductOrder with this data.
     */
    create: XOR<ProductOrderCreateInput, ProductOrderUncheckedCreateInput>
    /**
     * In case the ProductOrder was found with the provided `where` argument, update it with this data.
     */
    update: XOR<ProductOrderUpdateInput, ProductOrderUncheckedUpdateInput>
  }

  /**
   * ProductOrder delete
   */
  export type ProductOrderDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
    /**
     * Filter which ProductOrder to delete.
     */
    where: ProductOrderWhereUniqueInput
  }

  /**
   * ProductOrder deleteMany
   */
  export type ProductOrderDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which ProductOrders to delete
     */
    where?: ProductOrderWhereInput
    /**
     * Limit how many ProductOrders to delete.
     */
    limit?: number
  }

  /**
   * ProductOrder.phoneItems
   */
  export type ProductOrder$phoneItemsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    where?: PhoneItemWhereInput
    orderBy?: PhoneItemOrderByWithRelationInput | PhoneItemOrderByWithRelationInput[]
    cursor?: PhoneItemWhereUniqueInput
    take?: number
    skip?: number
    distinct?: PhoneItemScalarFieldEnum | PhoneItemScalarFieldEnum[]
  }

  /**
   * ProductOrder.assignLogs
   */
  export type ProductOrder$assignLogsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    where?: AssignLogWhereInput
    orderBy?: AssignLogOrderByWithRelationInput | AssignLogOrderByWithRelationInput[]
    cursor?: AssignLogWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AssignLogScalarFieldEnum | AssignLogScalarFieldEnum[]
  }

  /**
   * ProductOrder without action
   */
  export type ProductOrderDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the ProductOrder
     */
    select?: ProductOrderSelect<ExtArgs> | null
    /**
     * Omit specific fields from the ProductOrder
     */
    omit?: ProductOrderOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: ProductOrderInclude<ExtArgs> | null
  }


  /**
   * Model PhoneItem
   */

  export type AggregatePhoneItem = {
    _count: PhoneItemCountAggregateOutputType | null
    _avg: PhoneItemAvgAggregateOutputType | null
    _sum: PhoneItemSumAggregateOutputType | null
    _min: PhoneItemMinAggregateOutputType | null
    _max: PhoneItemMaxAggregateOutputType | null
  }

  export type PhoneItemAvgAggregateOutputType = {
    status: number | null
  }

  export type PhoneItemSumAggregateOutputType = {
    status: number | null
  }

  export type PhoneItemMinAggregateOutputType = {
    id: string | null
    phoneNumber: string | null
    status: number | null
    addedDate: Date | null
    tariffCode: string | null
    productOrderId: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type PhoneItemMaxAggregateOutputType = {
    id: string | null
    phoneNumber: string | null
    status: number | null
    addedDate: Date | null
    tariffCode: string | null
    productOrderId: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type PhoneItemCountAggregateOutputType = {
    id: number
    phoneNumber: number
    status: number
    addedDate: number
    tariffCode: number
    productOrderId: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type PhoneItemAvgAggregateInputType = {
    status?: true
  }

  export type PhoneItemSumAggregateInputType = {
    status?: true
  }

  export type PhoneItemMinAggregateInputType = {
    id?: true
    phoneNumber?: true
    status?: true
    addedDate?: true
    tariffCode?: true
    productOrderId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type PhoneItemMaxAggregateInputType = {
    id?: true
    phoneNumber?: true
    status?: true
    addedDate?: true
    tariffCode?: true
    productOrderId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type PhoneItemCountAggregateInputType = {
    id?: true
    phoneNumber?: true
    status?: true
    addedDate?: true
    tariffCode?: true
    productOrderId?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type PhoneItemAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which PhoneItem to aggregate.
     */
    where?: PhoneItemWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PhoneItems to fetch.
     */
    orderBy?: PhoneItemOrderByWithRelationInput | PhoneItemOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: PhoneItemWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PhoneItems from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PhoneItems.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned PhoneItems
    **/
    _count?: true | PhoneItemCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: PhoneItemAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: PhoneItemSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: PhoneItemMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: PhoneItemMaxAggregateInputType
  }

  export type GetPhoneItemAggregateType<T extends PhoneItemAggregateArgs> = {
        [P in keyof T & keyof AggregatePhoneItem]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregatePhoneItem[P]>
      : GetScalarType<T[P], AggregatePhoneItem[P]>
  }




  export type PhoneItemGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PhoneItemWhereInput
    orderBy?: PhoneItemOrderByWithAggregationInput | PhoneItemOrderByWithAggregationInput[]
    by: PhoneItemScalarFieldEnum[] | PhoneItemScalarFieldEnum
    having?: PhoneItemScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: PhoneItemCountAggregateInputType | true
    _avg?: PhoneItemAvgAggregateInputType
    _sum?: PhoneItemSumAggregateInputType
    _min?: PhoneItemMinAggregateInputType
    _max?: PhoneItemMaxAggregateInputType
  }

  export type PhoneItemGroupByOutputType = {
    id: string
    phoneNumber: string
    status: number
    addedDate: Date | null
    tariffCode: string | null
    productOrderId: string
    createdAt: Date
    updatedAt: Date
    _count: PhoneItemCountAggregateOutputType | null
    _avg: PhoneItemAvgAggregateOutputType | null
    _sum: PhoneItemSumAggregateOutputType | null
    _min: PhoneItemMinAggregateOutputType | null
    _max: PhoneItemMaxAggregateOutputType | null
  }

  type GetPhoneItemGroupByPayload<T extends PhoneItemGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<PhoneItemGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof PhoneItemGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], PhoneItemGroupByOutputType[P]>
            : GetScalarType<T[P], PhoneItemGroupByOutputType[P]>
        }
      >
    >


  export type PhoneItemSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    status?: boolean
    addedDate?: boolean
    tariffCode?: boolean
    productOrderId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["phoneItem"]>

  export type PhoneItemSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    status?: boolean
    addedDate?: boolean
    tariffCode?: boolean
    productOrderId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["phoneItem"]>

  export type PhoneItemSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    status?: boolean
    addedDate?: boolean
    tariffCode?: boolean
    productOrderId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["phoneItem"]>

  export type PhoneItemSelectScalar = {
    id?: boolean
    phoneNumber?: boolean
    status?: boolean
    addedDate?: boolean
    tariffCode?: boolean
    productOrderId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type PhoneItemOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "phoneNumber" | "status" | "addedDate" | "tariffCode" | "productOrderId" | "createdAt" | "updatedAt", ExtArgs["result"]["phoneItem"]>
  export type PhoneItemInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
  }
  export type PhoneItemIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
  }
  export type PhoneItemIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
  }

  export type $PhoneItemPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "PhoneItem"
    objects: {
      productOrder: Prisma.$ProductOrderPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      phoneNumber: string
      status: number
      addedDate: Date | null
      tariffCode: string | null
      productOrderId: string
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["phoneItem"]>
    composites: {}
  }

  type PhoneItemGetPayload<S extends boolean | null | undefined | PhoneItemDefaultArgs> = $Result.GetResult<Prisma.$PhoneItemPayload, S>

  type PhoneItemCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<PhoneItemFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: PhoneItemCountAggregateInputType | true
    }

  export interface PhoneItemDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['PhoneItem'], meta: { name: 'PhoneItem' } }
    /**
     * Find zero or one PhoneItem that matches the filter.
     * @param {PhoneItemFindUniqueArgs} args - Arguments to find a PhoneItem
     * @example
     * // Get one PhoneItem
     * const phoneItem = await prisma.phoneItem.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends PhoneItemFindUniqueArgs>(args: SelectSubset<T, PhoneItemFindUniqueArgs<ExtArgs>>): Prisma__PhoneItemClient<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one PhoneItem that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {PhoneItemFindUniqueOrThrowArgs} args - Arguments to find a PhoneItem
     * @example
     * // Get one PhoneItem
     * const phoneItem = await prisma.phoneItem.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends PhoneItemFindUniqueOrThrowArgs>(args: SelectSubset<T, PhoneItemFindUniqueOrThrowArgs<ExtArgs>>): Prisma__PhoneItemClient<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first PhoneItem that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PhoneItemFindFirstArgs} args - Arguments to find a PhoneItem
     * @example
     * // Get one PhoneItem
     * const phoneItem = await prisma.phoneItem.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends PhoneItemFindFirstArgs>(args?: SelectSubset<T, PhoneItemFindFirstArgs<ExtArgs>>): Prisma__PhoneItemClient<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first PhoneItem that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PhoneItemFindFirstOrThrowArgs} args - Arguments to find a PhoneItem
     * @example
     * // Get one PhoneItem
     * const phoneItem = await prisma.phoneItem.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends PhoneItemFindFirstOrThrowArgs>(args?: SelectSubset<T, PhoneItemFindFirstOrThrowArgs<ExtArgs>>): Prisma__PhoneItemClient<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more PhoneItems that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PhoneItemFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all PhoneItems
     * const phoneItems = await prisma.phoneItem.findMany()
     * 
     * // Get first 10 PhoneItems
     * const phoneItems = await prisma.phoneItem.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const phoneItemWithIdOnly = await prisma.phoneItem.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends PhoneItemFindManyArgs>(args?: SelectSubset<T, PhoneItemFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a PhoneItem.
     * @param {PhoneItemCreateArgs} args - Arguments to create a PhoneItem.
     * @example
     * // Create one PhoneItem
     * const PhoneItem = await prisma.phoneItem.create({
     *   data: {
     *     // ... data to create a PhoneItem
     *   }
     * })
     * 
     */
    create<T extends PhoneItemCreateArgs>(args: SelectSubset<T, PhoneItemCreateArgs<ExtArgs>>): Prisma__PhoneItemClient<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many PhoneItems.
     * @param {PhoneItemCreateManyArgs} args - Arguments to create many PhoneItems.
     * @example
     * // Create many PhoneItems
     * const phoneItem = await prisma.phoneItem.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends PhoneItemCreateManyArgs>(args?: SelectSubset<T, PhoneItemCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many PhoneItems and returns the data saved in the database.
     * @param {PhoneItemCreateManyAndReturnArgs} args - Arguments to create many PhoneItems.
     * @example
     * // Create many PhoneItems
     * const phoneItem = await prisma.phoneItem.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many PhoneItems and only return the `id`
     * const phoneItemWithIdOnly = await prisma.phoneItem.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends PhoneItemCreateManyAndReturnArgs>(args?: SelectSubset<T, PhoneItemCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a PhoneItem.
     * @param {PhoneItemDeleteArgs} args - Arguments to delete one PhoneItem.
     * @example
     * // Delete one PhoneItem
     * const PhoneItem = await prisma.phoneItem.delete({
     *   where: {
     *     // ... filter to delete one PhoneItem
     *   }
     * })
     * 
     */
    delete<T extends PhoneItemDeleteArgs>(args: SelectSubset<T, PhoneItemDeleteArgs<ExtArgs>>): Prisma__PhoneItemClient<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one PhoneItem.
     * @param {PhoneItemUpdateArgs} args - Arguments to update one PhoneItem.
     * @example
     * // Update one PhoneItem
     * const phoneItem = await prisma.phoneItem.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends PhoneItemUpdateArgs>(args: SelectSubset<T, PhoneItemUpdateArgs<ExtArgs>>): Prisma__PhoneItemClient<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more PhoneItems.
     * @param {PhoneItemDeleteManyArgs} args - Arguments to filter PhoneItems to delete.
     * @example
     * // Delete a few PhoneItems
     * const { count } = await prisma.phoneItem.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends PhoneItemDeleteManyArgs>(args?: SelectSubset<T, PhoneItemDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more PhoneItems.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PhoneItemUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many PhoneItems
     * const phoneItem = await prisma.phoneItem.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends PhoneItemUpdateManyArgs>(args: SelectSubset<T, PhoneItemUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more PhoneItems and returns the data updated in the database.
     * @param {PhoneItemUpdateManyAndReturnArgs} args - Arguments to update many PhoneItems.
     * @example
     * // Update many PhoneItems
     * const phoneItem = await prisma.phoneItem.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more PhoneItems and only return the `id`
     * const phoneItemWithIdOnly = await prisma.phoneItem.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends PhoneItemUpdateManyAndReturnArgs>(args: SelectSubset<T, PhoneItemUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one PhoneItem.
     * @param {PhoneItemUpsertArgs} args - Arguments to update or create a PhoneItem.
     * @example
     * // Update or create a PhoneItem
     * const phoneItem = await prisma.phoneItem.upsert({
     *   create: {
     *     // ... data to create a PhoneItem
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the PhoneItem we want to update
     *   }
     * })
     */
    upsert<T extends PhoneItemUpsertArgs>(args: SelectSubset<T, PhoneItemUpsertArgs<ExtArgs>>): Prisma__PhoneItemClient<$Result.GetResult<Prisma.$PhoneItemPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of PhoneItems.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PhoneItemCountArgs} args - Arguments to filter PhoneItems to count.
     * @example
     * // Count the number of PhoneItems
     * const count = await prisma.phoneItem.count({
     *   where: {
     *     // ... the filter for the PhoneItems we want to count
     *   }
     * })
    **/
    count<T extends PhoneItemCountArgs>(
      args?: Subset<T, PhoneItemCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], PhoneItemCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a PhoneItem.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PhoneItemAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends PhoneItemAggregateArgs>(args: Subset<T, PhoneItemAggregateArgs>): Prisma.PrismaPromise<GetPhoneItemAggregateType<T>>

    /**
     * Group by PhoneItem.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PhoneItemGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends PhoneItemGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: PhoneItemGroupByArgs['orderBy'] }
        : { orderBy?: PhoneItemGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, PhoneItemGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPhoneItemGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the PhoneItem model
   */
  readonly fields: PhoneItemFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for PhoneItem.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__PhoneItemClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    productOrder<T extends ProductOrderDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ProductOrderDefaultArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the PhoneItem model
   */
  interface PhoneItemFieldRefs {
    readonly id: FieldRef<"PhoneItem", 'String'>
    readonly phoneNumber: FieldRef<"PhoneItem", 'String'>
    readonly status: FieldRef<"PhoneItem", 'Int'>
    readonly addedDate: FieldRef<"PhoneItem", 'DateTime'>
    readonly tariffCode: FieldRef<"PhoneItem", 'String'>
    readonly productOrderId: FieldRef<"PhoneItem", 'String'>
    readonly createdAt: FieldRef<"PhoneItem", 'DateTime'>
    readonly updatedAt: FieldRef<"PhoneItem", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * PhoneItem findUnique
   */
  export type PhoneItemFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * Filter, which PhoneItem to fetch.
     */
    where: PhoneItemWhereUniqueInput
  }

  /**
   * PhoneItem findUniqueOrThrow
   */
  export type PhoneItemFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * Filter, which PhoneItem to fetch.
     */
    where: PhoneItemWhereUniqueInput
  }

  /**
   * PhoneItem findFirst
   */
  export type PhoneItemFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * Filter, which PhoneItem to fetch.
     */
    where?: PhoneItemWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PhoneItems to fetch.
     */
    orderBy?: PhoneItemOrderByWithRelationInput | PhoneItemOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for PhoneItems.
     */
    cursor?: PhoneItemWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PhoneItems from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PhoneItems.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of PhoneItems.
     */
    distinct?: PhoneItemScalarFieldEnum | PhoneItemScalarFieldEnum[]
  }

  /**
   * PhoneItem findFirstOrThrow
   */
  export type PhoneItemFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * Filter, which PhoneItem to fetch.
     */
    where?: PhoneItemWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PhoneItems to fetch.
     */
    orderBy?: PhoneItemOrderByWithRelationInput | PhoneItemOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for PhoneItems.
     */
    cursor?: PhoneItemWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PhoneItems from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PhoneItems.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of PhoneItems.
     */
    distinct?: PhoneItemScalarFieldEnum | PhoneItemScalarFieldEnum[]
  }

  /**
   * PhoneItem findMany
   */
  export type PhoneItemFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * Filter, which PhoneItems to fetch.
     */
    where?: PhoneItemWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of PhoneItems to fetch.
     */
    orderBy?: PhoneItemOrderByWithRelationInput | PhoneItemOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing PhoneItems.
     */
    cursor?: PhoneItemWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` PhoneItems from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` PhoneItems.
     */
    skip?: number
    distinct?: PhoneItemScalarFieldEnum | PhoneItemScalarFieldEnum[]
  }

  /**
   * PhoneItem create
   */
  export type PhoneItemCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * The data needed to create a PhoneItem.
     */
    data: XOR<PhoneItemCreateInput, PhoneItemUncheckedCreateInput>
  }

  /**
   * PhoneItem createMany
   */
  export type PhoneItemCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many PhoneItems.
     */
    data: PhoneItemCreateManyInput | PhoneItemCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * PhoneItem createManyAndReturn
   */
  export type PhoneItemCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * The data used to create many PhoneItems.
     */
    data: PhoneItemCreateManyInput | PhoneItemCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * PhoneItem update
   */
  export type PhoneItemUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * The data needed to update a PhoneItem.
     */
    data: XOR<PhoneItemUpdateInput, PhoneItemUncheckedUpdateInput>
    /**
     * Choose, which PhoneItem to update.
     */
    where: PhoneItemWhereUniqueInput
  }

  /**
   * PhoneItem updateMany
   */
  export type PhoneItemUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update PhoneItems.
     */
    data: XOR<PhoneItemUpdateManyMutationInput, PhoneItemUncheckedUpdateManyInput>
    /**
     * Filter which PhoneItems to update
     */
    where?: PhoneItemWhereInput
    /**
     * Limit how many PhoneItems to update.
     */
    limit?: number
  }

  /**
   * PhoneItem updateManyAndReturn
   */
  export type PhoneItemUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * The data used to update PhoneItems.
     */
    data: XOR<PhoneItemUpdateManyMutationInput, PhoneItemUncheckedUpdateManyInput>
    /**
     * Filter which PhoneItems to update
     */
    where?: PhoneItemWhereInput
    /**
     * Limit how many PhoneItems to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * PhoneItem upsert
   */
  export type PhoneItemUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * The filter to search for the PhoneItem to update in case it exists.
     */
    where: PhoneItemWhereUniqueInput
    /**
     * In case the PhoneItem found by the `where` argument doesn't exist, create a new PhoneItem with this data.
     */
    create: XOR<PhoneItemCreateInput, PhoneItemUncheckedCreateInput>
    /**
     * In case the PhoneItem was found with the provided `where` argument, update it with this data.
     */
    update: XOR<PhoneItemUpdateInput, PhoneItemUncheckedUpdateInput>
  }

  /**
   * PhoneItem delete
   */
  export type PhoneItemDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
    /**
     * Filter which PhoneItem to delete.
     */
    where: PhoneItemWhereUniqueInput
  }

  /**
   * PhoneItem deleteMany
   */
  export type PhoneItemDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which PhoneItems to delete
     */
    where?: PhoneItemWhereInput
    /**
     * Limit how many PhoneItems to delete.
     */
    limit?: number
  }

  /**
   * PhoneItem without action
   */
  export type PhoneItemDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the PhoneItem
     */
    select?: PhoneItemSelect<ExtArgs> | null
    /**
     * Omit specific fields from the PhoneItem
     */
    omit?: PhoneItemOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PhoneItemInclude<ExtArgs> | null
  }


  /**
   * Model AssignLog
   */

  export type AggregateAssignLog = {
    _count: AssignLogCountAggregateOutputType | null
    _avg: AssignLogAvgAggregateOutputType | null
    _sum: AssignLogSumAggregateOutputType | null
    _min: AssignLogMinAggregateOutputType | null
    _max: AssignLogMaxAggregateOutputType | null
  }

  export type AssignLogAvgAggregateOutputType = {
    id: number | null
    userId: number | null
  }

  export type AssignLogSumAggregateOutputType = {
    id: number | null
    userId: number | null
  }

  export type AssignLogMinAggregateOutputType = {
    id: number | null
    phoneNumber: string | null
    status: string | null
    message: string | null
    productOrderId: string | null
    userId: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AssignLogMaxAggregateOutputType = {
    id: number | null
    phoneNumber: string | null
    status: string | null
    message: string | null
    productOrderId: string | null
    userId: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type AssignLogCountAggregateOutputType = {
    id: number
    phoneNumber: number
    status: number
    message: number
    productOrderId: number
    userId: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type AssignLogAvgAggregateInputType = {
    id?: true
    userId?: true
  }

  export type AssignLogSumAggregateInputType = {
    id?: true
    userId?: true
  }

  export type AssignLogMinAggregateInputType = {
    id?: true
    phoneNumber?: true
    status?: true
    message?: true
    productOrderId?: true
    userId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AssignLogMaxAggregateInputType = {
    id?: true
    phoneNumber?: true
    status?: true
    message?: true
    productOrderId?: true
    userId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type AssignLogCountAggregateInputType = {
    id?: true
    phoneNumber?: true
    status?: true
    message?: true
    productOrderId?: true
    userId?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type AssignLogAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AssignLog to aggregate.
     */
    where?: AssignLogWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AssignLogs to fetch.
     */
    orderBy?: AssignLogOrderByWithRelationInput | AssignLogOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: AssignLogWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AssignLogs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AssignLogs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned AssignLogs
    **/
    _count?: true | AssignLogCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: AssignLogAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: AssignLogSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: AssignLogMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: AssignLogMaxAggregateInputType
  }

  export type GetAssignLogAggregateType<T extends AssignLogAggregateArgs> = {
        [P in keyof T & keyof AggregateAssignLog]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateAssignLog[P]>
      : GetScalarType<T[P], AggregateAssignLog[P]>
  }




  export type AssignLogGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: AssignLogWhereInput
    orderBy?: AssignLogOrderByWithAggregationInput | AssignLogOrderByWithAggregationInput[]
    by: AssignLogScalarFieldEnum[] | AssignLogScalarFieldEnum
    having?: AssignLogScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: AssignLogCountAggregateInputType | true
    _avg?: AssignLogAvgAggregateInputType
    _sum?: AssignLogSumAggregateInputType
    _min?: AssignLogMinAggregateInputType
    _max?: AssignLogMaxAggregateInputType
  }

  export type AssignLogGroupByOutputType = {
    id: number
    phoneNumber: string
    status: string
    message: string | null
    productOrderId: string
    userId: number
    createdAt: Date
    updatedAt: Date
    _count: AssignLogCountAggregateOutputType | null
    _avg: AssignLogAvgAggregateOutputType | null
    _sum: AssignLogSumAggregateOutputType | null
    _min: AssignLogMinAggregateOutputType | null
    _max: AssignLogMaxAggregateOutputType | null
  }

  type GetAssignLogGroupByPayload<T extends AssignLogGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<AssignLogGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof AssignLogGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], AssignLogGroupByOutputType[P]>
            : GetScalarType<T[P], AssignLogGroupByOutputType[P]>
        }
      >
    >


  export type AssignLogSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    status?: boolean
    message?: boolean
    productOrderId?: boolean
    userId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["assignLog"]>

  export type AssignLogSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    status?: boolean
    message?: boolean
    productOrderId?: boolean
    userId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["assignLog"]>

  export type AssignLogSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    status?: boolean
    message?: boolean
    productOrderId?: boolean
    userId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["assignLog"]>

  export type AssignLogSelectScalar = {
    id?: boolean
    phoneNumber?: boolean
    status?: boolean
    message?: boolean
    productOrderId?: boolean
    userId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type AssignLogOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "phoneNumber" | "status" | "message" | "productOrderId" | "userId" | "createdAt" | "updatedAt", ExtArgs["result"]["assignLog"]>
  export type AssignLogInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type AssignLogIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type AssignLogIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    productOrder?: boolean | ProductOrderDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $AssignLogPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "AssignLog"
    objects: {
      productOrder: Prisma.$ProductOrderPayload<ExtArgs>
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      phoneNumber: string
      status: string
      message: string | null
      productOrderId: string
      userId: number
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["assignLog"]>
    composites: {}
  }

  type AssignLogGetPayload<S extends boolean | null | undefined | AssignLogDefaultArgs> = $Result.GetResult<Prisma.$AssignLogPayload, S>

  type AssignLogCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<AssignLogFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: AssignLogCountAggregateInputType | true
    }

  export interface AssignLogDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['AssignLog'], meta: { name: 'AssignLog' } }
    /**
     * Find zero or one AssignLog that matches the filter.
     * @param {AssignLogFindUniqueArgs} args - Arguments to find a AssignLog
     * @example
     * // Get one AssignLog
     * const assignLog = await prisma.assignLog.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends AssignLogFindUniqueArgs>(args: SelectSubset<T, AssignLogFindUniqueArgs<ExtArgs>>): Prisma__AssignLogClient<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one AssignLog that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {AssignLogFindUniqueOrThrowArgs} args - Arguments to find a AssignLog
     * @example
     * // Get one AssignLog
     * const assignLog = await prisma.assignLog.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends AssignLogFindUniqueOrThrowArgs>(args: SelectSubset<T, AssignLogFindUniqueOrThrowArgs<ExtArgs>>): Prisma__AssignLogClient<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AssignLog that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignLogFindFirstArgs} args - Arguments to find a AssignLog
     * @example
     * // Get one AssignLog
     * const assignLog = await prisma.assignLog.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends AssignLogFindFirstArgs>(args?: SelectSubset<T, AssignLogFindFirstArgs<ExtArgs>>): Prisma__AssignLogClient<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first AssignLog that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignLogFindFirstOrThrowArgs} args - Arguments to find a AssignLog
     * @example
     * // Get one AssignLog
     * const assignLog = await prisma.assignLog.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends AssignLogFindFirstOrThrowArgs>(args?: SelectSubset<T, AssignLogFindFirstOrThrowArgs<ExtArgs>>): Prisma__AssignLogClient<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more AssignLogs that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignLogFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all AssignLogs
     * const assignLogs = await prisma.assignLog.findMany()
     * 
     * // Get first 10 AssignLogs
     * const assignLogs = await prisma.assignLog.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const assignLogWithIdOnly = await prisma.assignLog.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends AssignLogFindManyArgs>(args?: SelectSubset<T, AssignLogFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a AssignLog.
     * @param {AssignLogCreateArgs} args - Arguments to create a AssignLog.
     * @example
     * // Create one AssignLog
     * const AssignLog = await prisma.assignLog.create({
     *   data: {
     *     // ... data to create a AssignLog
     *   }
     * })
     * 
     */
    create<T extends AssignLogCreateArgs>(args: SelectSubset<T, AssignLogCreateArgs<ExtArgs>>): Prisma__AssignLogClient<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many AssignLogs.
     * @param {AssignLogCreateManyArgs} args - Arguments to create many AssignLogs.
     * @example
     * // Create many AssignLogs
     * const assignLog = await prisma.assignLog.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends AssignLogCreateManyArgs>(args?: SelectSubset<T, AssignLogCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many AssignLogs and returns the data saved in the database.
     * @param {AssignLogCreateManyAndReturnArgs} args - Arguments to create many AssignLogs.
     * @example
     * // Create many AssignLogs
     * const assignLog = await prisma.assignLog.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many AssignLogs and only return the `id`
     * const assignLogWithIdOnly = await prisma.assignLog.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends AssignLogCreateManyAndReturnArgs>(args?: SelectSubset<T, AssignLogCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a AssignLog.
     * @param {AssignLogDeleteArgs} args - Arguments to delete one AssignLog.
     * @example
     * // Delete one AssignLog
     * const AssignLog = await prisma.assignLog.delete({
     *   where: {
     *     // ... filter to delete one AssignLog
     *   }
     * })
     * 
     */
    delete<T extends AssignLogDeleteArgs>(args: SelectSubset<T, AssignLogDeleteArgs<ExtArgs>>): Prisma__AssignLogClient<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one AssignLog.
     * @param {AssignLogUpdateArgs} args - Arguments to update one AssignLog.
     * @example
     * // Update one AssignLog
     * const assignLog = await prisma.assignLog.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends AssignLogUpdateArgs>(args: SelectSubset<T, AssignLogUpdateArgs<ExtArgs>>): Prisma__AssignLogClient<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more AssignLogs.
     * @param {AssignLogDeleteManyArgs} args - Arguments to filter AssignLogs to delete.
     * @example
     * // Delete a few AssignLogs
     * const { count } = await prisma.assignLog.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends AssignLogDeleteManyArgs>(args?: SelectSubset<T, AssignLogDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AssignLogs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignLogUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many AssignLogs
     * const assignLog = await prisma.assignLog.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends AssignLogUpdateManyArgs>(args: SelectSubset<T, AssignLogUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more AssignLogs and returns the data updated in the database.
     * @param {AssignLogUpdateManyAndReturnArgs} args - Arguments to update many AssignLogs.
     * @example
     * // Update many AssignLogs
     * const assignLog = await prisma.assignLog.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more AssignLogs and only return the `id`
     * const assignLogWithIdOnly = await prisma.assignLog.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends AssignLogUpdateManyAndReturnArgs>(args: SelectSubset<T, AssignLogUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one AssignLog.
     * @param {AssignLogUpsertArgs} args - Arguments to update or create a AssignLog.
     * @example
     * // Update or create a AssignLog
     * const assignLog = await prisma.assignLog.upsert({
     *   create: {
     *     // ... data to create a AssignLog
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the AssignLog we want to update
     *   }
     * })
     */
    upsert<T extends AssignLogUpsertArgs>(args: SelectSubset<T, AssignLogUpsertArgs<ExtArgs>>): Prisma__AssignLogClient<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of AssignLogs.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignLogCountArgs} args - Arguments to filter AssignLogs to count.
     * @example
     * // Count the number of AssignLogs
     * const count = await prisma.assignLog.count({
     *   where: {
     *     // ... the filter for the AssignLogs we want to count
     *   }
     * })
    **/
    count<T extends AssignLogCountArgs>(
      args?: Subset<T, AssignLogCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], AssignLogCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a AssignLog.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignLogAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends AssignLogAggregateArgs>(args: Subset<T, AssignLogAggregateArgs>): Prisma.PrismaPromise<GetAssignLogAggregateType<T>>

    /**
     * Group by AssignLog.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {AssignLogGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends AssignLogGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: AssignLogGroupByArgs['orderBy'] }
        : { orderBy?: AssignLogGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, AssignLogGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetAssignLogGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the AssignLog model
   */
  readonly fields: AssignLogFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for AssignLog.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__AssignLogClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    productOrder<T extends ProductOrderDefaultArgs<ExtArgs> = {}>(args?: Subset<T, ProductOrderDefaultArgs<ExtArgs>>): Prisma__ProductOrderClient<$Result.GetResult<Prisma.$ProductOrderPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the AssignLog model
   */
  interface AssignLogFieldRefs {
    readonly id: FieldRef<"AssignLog", 'Int'>
    readonly phoneNumber: FieldRef<"AssignLog", 'String'>
    readonly status: FieldRef<"AssignLog", 'String'>
    readonly message: FieldRef<"AssignLog", 'String'>
    readonly productOrderId: FieldRef<"AssignLog", 'String'>
    readonly userId: FieldRef<"AssignLog", 'Int'>
    readonly createdAt: FieldRef<"AssignLog", 'DateTime'>
    readonly updatedAt: FieldRef<"AssignLog", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * AssignLog findUnique
   */
  export type AssignLogFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * Filter, which AssignLog to fetch.
     */
    where: AssignLogWhereUniqueInput
  }

  /**
   * AssignLog findUniqueOrThrow
   */
  export type AssignLogFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * Filter, which AssignLog to fetch.
     */
    where: AssignLogWhereUniqueInput
  }

  /**
   * AssignLog findFirst
   */
  export type AssignLogFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * Filter, which AssignLog to fetch.
     */
    where?: AssignLogWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AssignLogs to fetch.
     */
    orderBy?: AssignLogOrderByWithRelationInput | AssignLogOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AssignLogs.
     */
    cursor?: AssignLogWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AssignLogs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AssignLogs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AssignLogs.
     */
    distinct?: AssignLogScalarFieldEnum | AssignLogScalarFieldEnum[]
  }

  /**
   * AssignLog findFirstOrThrow
   */
  export type AssignLogFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * Filter, which AssignLog to fetch.
     */
    where?: AssignLogWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AssignLogs to fetch.
     */
    orderBy?: AssignLogOrderByWithRelationInput | AssignLogOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for AssignLogs.
     */
    cursor?: AssignLogWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AssignLogs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AssignLogs.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of AssignLogs.
     */
    distinct?: AssignLogScalarFieldEnum | AssignLogScalarFieldEnum[]
  }

  /**
   * AssignLog findMany
   */
  export type AssignLogFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * Filter, which AssignLogs to fetch.
     */
    where?: AssignLogWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of AssignLogs to fetch.
     */
    orderBy?: AssignLogOrderByWithRelationInput | AssignLogOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing AssignLogs.
     */
    cursor?: AssignLogWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` AssignLogs from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` AssignLogs.
     */
    skip?: number
    distinct?: AssignLogScalarFieldEnum | AssignLogScalarFieldEnum[]
  }

  /**
   * AssignLog create
   */
  export type AssignLogCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * The data needed to create a AssignLog.
     */
    data: XOR<AssignLogCreateInput, AssignLogUncheckedCreateInput>
  }

  /**
   * AssignLog createMany
   */
  export type AssignLogCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many AssignLogs.
     */
    data: AssignLogCreateManyInput | AssignLogCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * AssignLog createManyAndReturn
   */
  export type AssignLogCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * The data used to create many AssignLogs.
     */
    data: AssignLogCreateManyInput | AssignLogCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * AssignLog update
   */
  export type AssignLogUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * The data needed to update a AssignLog.
     */
    data: XOR<AssignLogUpdateInput, AssignLogUncheckedUpdateInput>
    /**
     * Choose, which AssignLog to update.
     */
    where: AssignLogWhereUniqueInput
  }

  /**
   * AssignLog updateMany
   */
  export type AssignLogUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update AssignLogs.
     */
    data: XOR<AssignLogUpdateManyMutationInput, AssignLogUncheckedUpdateManyInput>
    /**
     * Filter which AssignLogs to update
     */
    where?: AssignLogWhereInput
    /**
     * Limit how many AssignLogs to update.
     */
    limit?: number
  }

  /**
   * AssignLog updateManyAndReturn
   */
  export type AssignLogUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * The data used to update AssignLogs.
     */
    data: XOR<AssignLogUpdateManyMutationInput, AssignLogUncheckedUpdateManyInput>
    /**
     * Filter which AssignLogs to update
     */
    where?: AssignLogWhereInput
    /**
     * Limit how many AssignLogs to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * AssignLog upsert
   */
  export type AssignLogUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * The filter to search for the AssignLog to update in case it exists.
     */
    where: AssignLogWhereUniqueInput
    /**
     * In case the AssignLog found by the `where` argument doesn't exist, create a new AssignLog with this data.
     */
    create: XOR<AssignLogCreateInput, AssignLogUncheckedCreateInput>
    /**
     * In case the AssignLog was found with the provided `where` argument, update it with this data.
     */
    update: XOR<AssignLogUpdateInput, AssignLogUncheckedUpdateInput>
  }

  /**
   * AssignLog delete
   */
  export type AssignLogDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    /**
     * Filter which AssignLog to delete.
     */
    where: AssignLogWhereUniqueInput
  }

  /**
   * AssignLog deleteMany
   */
  export type AssignLogDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which AssignLogs to delete
     */
    where?: AssignLogWhereInput
    /**
     * Limit how many AssignLogs to delete.
     */
    limit?: number
  }

  /**
   * AssignLog without action
   */
  export type AssignLogDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
  }


  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserAvgAggregateOutputType = {
    id: number | null
    storageId: number | null
  }

  export type UserSumAggregateOutputType = {
    id: number | null
    storageId: number | null
  }

  export type UserMinAggregateOutputType = {
    id: number | null
    phoneNumber: string | null
    uid: string | null
    name: string | null
    status: string | null
    storageId: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserMaxAggregateOutputType = {
    id: number | null
    phoneNumber: string | null
    uid: string | null
    name: string | null
    status: string | null
    storageId: number | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    phoneNumber: number
    uid: number
    name: number
    status: number
    storageId: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type UserAvgAggregateInputType = {
    id?: true
    storageId?: true
  }

  export type UserSumAggregateInputType = {
    id?: true
    storageId?: true
  }

  export type UserMinAggregateInputType = {
    id?: true
    phoneNumber?: true
    uid?: true
    name?: true
    status?: true
    storageId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    phoneNumber?: true
    uid?: true
    name?: true
    status?: true
    storageId?: true
    createdAt?: true
    updatedAt?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    phoneNumber?: true
    uid?: true
    name?: true
    status?: true
    storageId?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: UserAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: UserSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _avg?: UserAvgAggregateInputType
    _sum?: UserSumAggregateInputType
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: number
    phoneNumber: string
    uid: string | null
    name: string | null
    status: string
    storageId: number
    createdAt: Date
    updatedAt: Date
    _count: UserCountAggregateOutputType | null
    _avg: UserAvgAggregateOutputType | null
    _sum: UserSumAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    uid?: boolean
    name?: boolean
    status?: boolean
    storageId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    storage?: boolean | StorageDefaultArgs<ExtArgs>
    assignLogs?: boolean | User$assignLogsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    uid?: boolean
    name?: boolean
    status?: boolean
    storageId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    storage?: boolean | StorageDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    phoneNumber?: boolean
    uid?: boolean
    name?: boolean
    status?: boolean
    storageId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    storage?: boolean | StorageDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    phoneNumber?: boolean
    uid?: boolean
    name?: boolean
    status?: boolean
    storageId?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type UserOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "phoneNumber" | "uid" | "name" | "status" | "storageId" | "createdAt" | "updatedAt", ExtArgs["result"]["user"]>
  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    storage?: boolean | StorageDefaultArgs<ExtArgs>
    assignLogs?: boolean | User$assignLogsArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    storage?: boolean | StorageDefaultArgs<ExtArgs>
  }
  export type UserIncludeUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    storage?: boolean | StorageDefaultArgs<ExtArgs>
  }

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      storage: Prisma.$StoragePayload<ExtArgs>
      assignLogs: Prisma.$AssignLogPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: number
      phoneNumber: string
      uid: string | null
      name: string | null
      status: string
      storageId: number
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users and returns the data updated in the database.
     * @param {UserUpdateManyAndReturnArgs} args - Arguments to update many Users.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Users and only return the `id`
     * const userWithIdOnly = await prisma.user.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends UserUpdateManyAndReturnArgs>(args: SelectSubset<T, UserUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    storage<T extends StorageDefaultArgs<ExtArgs> = {}>(args?: Subset<T, StorageDefaultArgs<ExtArgs>>): Prisma__StorageClient<$Result.GetResult<Prisma.$StoragePayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions> | Null, Null, ExtArgs, GlobalOmitOptions>
    assignLogs<T extends User$assignLogsArgs<ExtArgs> = {}>(args?: Subset<T, User$assignLogsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$AssignLogPayload<ExtArgs>, T, "findMany", GlobalOmitOptions> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'Int'>
    readonly phoneNumber: FieldRef<"User", 'String'>
    readonly uid: FieldRef<"User", 'String'>
    readonly name: FieldRef<"User", 'String'>
    readonly status: FieldRef<"User", 'String'>
    readonly storageId: FieldRef<"User", 'Int'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
  }

  /**
   * User updateManyAndReturn
   */
  export type UserUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to update.
     */
    limit?: number
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserIncludeUpdateManyAndReturn<ExtArgs> | null
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
    /**
     * Limit how many Users to delete.
     */
    limit?: number
  }

  /**
   * User.assignLogs
   */
  export type User$assignLogsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the AssignLog
     */
    select?: AssignLogSelect<ExtArgs> | null
    /**
     * Omit specific fields from the AssignLog
     */
    omit?: AssignLogOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: AssignLogInclude<ExtArgs> | null
    where?: AssignLogWhereInput
    orderBy?: AssignLogOrderByWithRelationInput | AssignLogOrderByWithRelationInput[]
    cursor?: AssignLogWhereUniqueInput
    take?: number
    skip?: number
    distinct?: AssignLogScalarFieldEnum | AssignLogScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Omit specific fields from the User
     */
    omit?: UserOmit<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model Task
   */

  export type AggregateTask = {
    _count: TaskCountAggregateOutputType | null
    _min: TaskMinAggregateOutputType | null
    _max: TaskMaxAggregateOutputType | null
  }

  export type TaskMinAggregateOutputType = {
    id: string | null
    type: string | null
    status: string | null
    message: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type TaskMaxAggregateOutputType = {
    id: string | null
    type: string | null
    status: string | null
    message: string | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type TaskCountAggregateOutputType = {
    id: number
    type: number
    status: number
    data: number
    result: number
    message: number
    createdAt: number
    updatedAt: number
    _all: number
  }


  export type TaskMinAggregateInputType = {
    id?: true
    type?: true
    status?: true
    message?: true
    createdAt?: true
    updatedAt?: true
  }

  export type TaskMaxAggregateInputType = {
    id?: true
    type?: true
    status?: true
    message?: true
    createdAt?: true
    updatedAt?: true
  }

  export type TaskCountAggregateInputType = {
    id?: true
    type?: true
    status?: true
    data?: true
    result?: true
    message?: true
    createdAt?: true
    updatedAt?: true
    _all?: true
  }

  export type TaskAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Task to aggregate.
     */
    where?: TaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Tasks to fetch.
     */
    orderBy?: TaskOrderByWithRelationInput | TaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: TaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Tasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Tasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Tasks
    **/
    _count?: true | TaskCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: TaskMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: TaskMaxAggregateInputType
  }

  export type GetTaskAggregateType<T extends TaskAggregateArgs> = {
        [P in keyof T & keyof AggregateTask]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateTask[P]>
      : GetScalarType<T[P], AggregateTask[P]>
  }




  export type TaskGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: TaskWhereInput
    orderBy?: TaskOrderByWithAggregationInput | TaskOrderByWithAggregationInput[]
    by: TaskScalarFieldEnum[] | TaskScalarFieldEnum
    having?: TaskScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: TaskCountAggregateInputType | true
    _min?: TaskMinAggregateInputType
    _max?: TaskMaxAggregateInputType
  }

  export type TaskGroupByOutputType = {
    id: string
    type: string
    status: string
    data: JsonValue
    result: JsonValue | null
    message: string | null
    createdAt: Date
    updatedAt: Date
    _count: TaskCountAggregateOutputType | null
    _min: TaskMinAggregateOutputType | null
    _max: TaskMaxAggregateOutputType | null
  }

  type GetTaskGroupByPayload<T extends TaskGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<TaskGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof TaskGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], TaskGroupByOutputType[P]>
            : GetScalarType<T[P], TaskGroupByOutputType[P]>
        }
      >
    >


  export type TaskSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    type?: boolean
    status?: boolean
    data?: boolean
    result?: boolean
    message?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["task"]>

  export type TaskSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    type?: boolean
    status?: boolean
    data?: boolean
    result?: boolean
    message?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["task"]>

  export type TaskSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    type?: boolean
    status?: boolean
    data?: boolean
    result?: boolean
    message?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }, ExtArgs["result"]["task"]>

  export type TaskSelectScalar = {
    id?: boolean
    type?: boolean
    status?: boolean
    data?: boolean
    result?: boolean
    message?: boolean
    createdAt?: boolean
    updatedAt?: boolean
  }

  export type TaskOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "type" | "status" | "data" | "result" | "message" | "createdAt" | "updatedAt", ExtArgs["result"]["task"]>

  export type $TaskPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Task"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: string
      type: string
      status: string
      data: Prisma.JsonValue
      result: Prisma.JsonValue | null
      message: string | null
      createdAt: Date
      updatedAt: Date
    }, ExtArgs["result"]["task"]>
    composites: {}
  }

  type TaskGetPayload<S extends boolean | null | undefined | TaskDefaultArgs> = $Result.GetResult<Prisma.$TaskPayload, S>

  type TaskCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<TaskFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: TaskCountAggregateInputType | true
    }

  export interface TaskDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Task'], meta: { name: 'Task' } }
    /**
     * Find zero or one Task that matches the filter.
     * @param {TaskFindUniqueArgs} args - Arguments to find a Task
     * @example
     * // Get one Task
     * const task = await prisma.task.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends TaskFindUniqueArgs>(args: SelectSubset<T, TaskFindUniqueArgs<ExtArgs>>): Prisma__TaskClient<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one Task that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {TaskFindUniqueOrThrowArgs} args - Arguments to find a Task
     * @example
     * // Get one Task
     * const task = await prisma.task.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends TaskFindUniqueOrThrowArgs>(args: SelectSubset<T, TaskFindUniqueOrThrowArgs<ExtArgs>>): Prisma__TaskClient<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Task that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TaskFindFirstArgs} args - Arguments to find a Task
     * @example
     * // Get one Task
     * const task = await prisma.task.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends TaskFindFirstArgs>(args?: SelectSubset<T, TaskFindFirstArgs<ExtArgs>>): Prisma__TaskClient<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first Task that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TaskFindFirstOrThrowArgs} args - Arguments to find a Task
     * @example
     * // Get one Task
     * const task = await prisma.task.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends TaskFindFirstOrThrowArgs>(args?: SelectSubset<T, TaskFindFirstOrThrowArgs<ExtArgs>>): Prisma__TaskClient<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more Tasks that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TaskFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Tasks
     * const tasks = await prisma.task.findMany()
     * 
     * // Get first 10 Tasks
     * const tasks = await prisma.task.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const taskWithIdOnly = await prisma.task.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends TaskFindManyArgs>(args?: SelectSubset<T, TaskFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a Task.
     * @param {TaskCreateArgs} args - Arguments to create a Task.
     * @example
     * // Create one Task
     * const Task = await prisma.task.create({
     *   data: {
     *     // ... data to create a Task
     *   }
     * })
     * 
     */
    create<T extends TaskCreateArgs>(args: SelectSubset<T, TaskCreateArgs<ExtArgs>>): Prisma__TaskClient<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many Tasks.
     * @param {TaskCreateManyArgs} args - Arguments to create many Tasks.
     * @example
     * // Create many Tasks
     * const task = await prisma.task.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends TaskCreateManyArgs>(args?: SelectSubset<T, TaskCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Tasks and returns the data saved in the database.
     * @param {TaskCreateManyAndReturnArgs} args - Arguments to create many Tasks.
     * @example
     * // Create many Tasks
     * const task = await prisma.task.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Tasks and only return the `id`
     * const taskWithIdOnly = await prisma.task.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends TaskCreateManyAndReturnArgs>(args?: SelectSubset<T, TaskCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a Task.
     * @param {TaskDeleteArgs} args - Arguments to delete one Task.
     * @example
     * // Delete one Task
     * const Task = await prisma.task.delete({
     *   where: {
     *     // ... filter to delete one Task
     *   }
     * })
     * 
     */
    delete<T extends TaskDeleteArgs>(args: SelectSubset<T, TaskDeleteArgs<ExtArgs>>): Prisma__TaskClient<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one Task.
     * @param {TaskUpdateArgs} args - Arguments to update one Task.
     * @example
     * // Update one Task
     * const task = await prisma.task.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends TaskUpdateArgs>(args: SelectSubset<T, TaskUpdateArgs<ExtArgs>>): Prisma__TaskClient<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more Tasks.
     * @param {TaskDeleteManyArgs} args - Arguments to filter Tasks to delete.
     * @example
     * // Delete a few Tasks
     * const { count } = await prisma.task.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends TaskDeleteManyArgs>(args?: SelectSubset<T, TaskDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Tasks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TaskUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Tasks
     * const task = await prisma.task.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends TaskUpdateManyArgs>(args: SelectSubset<T, TaskUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Tasks and returns the data updated in the database.
     * @param {TaskUpdateManyAndReturnArgs} args - Arguments to update many Tasks.
     * @example
     * // Update many Tasks
     * const task = await prisma.task.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more Tasks and only return the `id`
     * const taskWithIdOnly = await prisma.task.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends TaskUpdateManyAndReturnArgs>(args: SelectSubset<T, TaskUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one Task.
     * @param {TaskUpsertArgs} args - Arguments to update or create a Task.
     * @example
     * // Update or create a Task
     * const task = await prisma.task.upsert({
     *   create: {
     *     // ... data to create a Task
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Task we want to update
     *   }
     * })
     */
    upsert<T extends TaskUpsertArgs>(args: SelectSubset<T, TaskUpsertArgs<ExtArgs>>): Prisma__TaskClient<$Result.GetResult<Prisma.$TaskPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of Tasks.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TaskCountArgs} args - Arguments to filter Tasks to count.
     * @example
     * // Count the number of Tasks
     * const count = await prisma.task.count({
     *   where: {
     *     // ... the filter for the Tasks we want to count
     *   }
     * })
    **/
    count<T extends TaskCountArgs>(
      args?: Subset<T, TaskCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], TaskCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Task.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TaskAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends TaskAggregateArgs>(args: Subset<T, TaskAggregateArgs>): Prisma.PrismaPromise<GetTaskAggregateType<T>>

    /**
     * Group by Task.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {TaskGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends TaskGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: TaskGroupByArgs['orderBy'] }
        : { orderBy?: TaskGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, TaskGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetTaskGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Task model
   */
  readonly fields: TaskFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Task.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__TaskClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Task model
   */
  interface TaskFieldRefs {
    readonly id: FieldRef<"Task", 'String'>
    readonly type: FieldRef<"Task", 'String'>
    readonly status: FieldRef<"Task", 'String'>
    readonly data: FieldRef<"Task", 'Json'>
    readonly result: FieldRef<"Task", 'Json'>
    readonly message: FieldRef<"Task", 'String'>
    readonly createdAt: FieldRef<"Task", 'DateTime'>
    readonly updatedAt: FieldRef<"Task", 'DateTime'>
  }
    

  // Custom InputTypes
  /**
   * Task findUnique
   */
  export type TaskFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * Filter, which Task to fetch.
     */
    where: TaskWhereUniqueInput
  }

  /**
   * Task findUniqueOrThrow
   */
  export type TaskFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * Filter, which Task to fetch.
     */
    where: TaskWhereUniqueInput
  }

  /**
   * Task findFirst
   */
  export type TaskFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * Filter, which Task to fetch.
     */
    where?: TaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Tasks to fetch.
     */
    orderBy?: TaskOrderByWithRelationInput | TaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Tasks.
     */
    cursor?: TaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Tasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Tasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Tasks.
     */
    distinct?: TaskScalarFieldEnum | TaskScalarFieldEnum[]
  }

  /**
   * Task findFirstOrThrow
   */
  export type TaskFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * Filter, which Task to fetch.
     */
    where?: TaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Tasks to fetch.
     */
    orderBy?: TaskOrderByWithRelationInput | TaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Tasks.
     */
    cursor?: TaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Tasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Tasks.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Tasks.
     */
    distinct?: TaskScalarFieldEnum | TaskScalarFieldEnum[]
  }

  /**
   * Task findMany
   */
  export type TaskFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * Filter, which Tasks to fetch.
     */
    where?: TaskWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Tasks to fetch.
     */
    orderBy?: TaskOrderByWithRelationInput | TaskOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Tasks.
     */
    cursor?: TaskWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Tasks from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Tasks.
     */
    skip?: number
    distinct?: TaskScalarFieldEnum | TaskScalarFieldEnum[]
  }

  /**
   * Task create
   */
  export type TaskCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * The data needed to create a Task.
     */
    data: XOR<TaskCreateInput, TaskUncheckedCreateInput>
  }

  /**
   * Task createMany
   */
  export type TaskCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Tasks.
     */
    data: TaskCreateManyInput | TaskCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Task createManyAndReturn
   */
  export type TaskCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * The data used to create many Tasks.
     */
    data: TaskCreateManyInput | TaskCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Task update
   */
  export type TaskUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * The data needed to update a Task.
     */
    data: XOR<TaskUpdateInput, TaskUncheckedUpdateInput>
    /**
     * Choose, which Task to update.
     */
    where: TaskWhereUniqueInput
  }

  /**
   * Task updateMany
   */
  export type TaskUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Tasks.
     */
    data: XOR<TaskUpdateManyMutationInput, TaskUncheckedUpdateManyInput>
    /**
     * Filter which Tasks to update
     */
    where?: TaskWhereInput
    /**
     * Limit how many Tasks to update.
     */
    limit?: number
  }

  /**
   * Task updateManyAndReturn
   */
  export type TaskUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * The data used to update Tasks.
     */
    data: XOR<TaskUpdateManyMutationInput, TaskUncheckedUpdateManyInput>
    /**
     * Filter which Tasks to update
     */
    where?: TaskWhereInput
    /**
     * Limit how many Tasks to update.
     */
    limit?: number
  }

  /**
   * Task upsert
   */
  export type TaskUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * The filter to search for the Task to update in case it exists.
     */
    where: TaskWhereUniqueInput
    /**
     * In case the Task found by the `where` argument doesn't exist, create a new Task with this data.
     */
    create: XOR<TaskCreateInput, TaskUncheckedCreateInput>
    /**
     * In case the Task was found with the provided `where` argument, update it with this data.
     */
    update: XOR<TaskUpdateInput, TaskUncheckedUpdateInput>
  }

  /**
   * Task delete
   */
  export type TaskDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
    /**
     * Filter which Task to delete.
     */
    where: TaskWhereUniqueInput
  }

  /**
   * Task deleteMany
   */
  export type TaskDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Tasks to delete
     */
    where?: TaskWhereInput
    /**
     * Limit how many Tasks to delete.
     */
    limit?: number
  }

  /**
   * Task without action
   */
  export type TaskDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Task
     */
    select?: TaskSelect<ExtArgs> | null
    /**
     * Omit specific fields from the Task
     */
    omit?: TaskOmit<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const StorageScalarFieldEnum: {
    id: 'id',
    username: 'username',
    password: 'password',
    otp: 'otp',
    code: 'code',
    status: 'status',
    message: 'message',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type StorageScalarFieldEnum = (typeof StorageScalarFieldEnum)[keyof typeof StorageScalarFieldEnum]


  export const ProductOrderScalarFieldEnum: {
    id: 'id',
    name: 'name',
    status: 'status',
    tariffId: 'tariffId',
    orderInfoId: 'orderInfoId',
    tariffName: 'tariffName',
    pckCode: 'pckCode',
    orderDate: 'orderDate',
    storageId: 'storageId',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type ProductOrderScalarFieldEnum = (typeof ProductOrderScalarFieldEnum)[keyof typeof ProductOrderScalarFieldEnum]


  export const PhoneItemScalarFieldEnum: {
    id: 'id',
    phoneNumber: 'phoneNumber',
    status: 'status',
    addedDate: 'addedDate',
    tariffCode: 'tariffCode',
    productOrderId: 'productOrderId',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type PhoneItemScalarFieldEnum = (typeof PhoneItemScalarFieldEnum)[keyof typeof PhoneItemScalarFieldEnum]


  export const AssignLogScalarFieldEnum: {
    id: 'id',
    phoneNumber: 'phoneNumber',
    status: 'status',
    message: 'message',
    productOrderId: 'productOrderId',
    userId: 'userId',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type AssignLogScalarFieldEnum = (typeof AssignLogScalarFieldEnum)[keyof typeof AssignLogScalarFieldEnum]


  export const UserScalarFieldEnum: {
    id: 'id',
    phoneNumber: 'phoneNumber',
    uid: 'uid',
    name: 'name',
    status: 'status',
    storageId: 'storageId',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const TaskScalarFieldEnum: {
    id: 'id',
    type: 'type',
    status: 'status',
    data: 'data',
    result: 'result',
    message: 'message',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  };

  export type TaskScalarFieldEnum = (typeof TaskScalarFieldEnum)[keyof typeof TaskScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const JsonNullValueInput: {
    JsonNull: typeof JsonNull
  };

  export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


  export const NullableJsonNullValueInput: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull
  };

  export type NullableJsonNullValueInput = (typeof NullableJsonNullValueInput)[keyof typeof NullableJsonNullValueInput]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    
  /**
   * Deep Input Types
   */


  export type StorageWhereInput = {
    AND?: StorageWhereInput | StorageWhereInput[]
    OR?: StorageWhereInput[]
    NOT?: StorageWhereInput | StorageWhereInput[]
    id?: IntFilter<"Storage"> | number
    username?: StringFilter<"Storage"> | string
    password?: StringFilter<"Storage"> | string
    otp?: StringFilter<"Storage"> | string
    code?: StringFilter<"Storage"> | string
    status?: StringFilter<"Storage"> | string
    message?: StringNullableFilter<"Storage"> | string | null
    createdAt?: DateTimeFilter<"Storage"> | Date | string
    updatedAt?: DateTimeFilter<"Storage"> | Date | string
    users?: UserListRelationFilter
    productOrders?: ProductOrderListRelationFilter
  }

  export type StorageOrderByWithRelationInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    otp?: SortOrder
    code?: SortOrder
    status?: SortOrder
    message?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    users?: UserOrderByRelationAggregateInput
    productOrders?: ProductOrderOrderByRelationAggregateInput
  }

  export type StorageWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    code?: string
    AND?: StorageWhereInput | StorageWhereInput[]
    OR?: StorageWhereInput[]
    NOT?: StorageWhereInput | StorageWhereInput[]
    username?: StringFilter<"Storage"> | string
    password?: StringFilter<"Storage"> | string
    otp?: StringFilter<"Storage"> | string
    status?: StringFilter<"Storage"> | string
    message?: StringNullableFilter<"Storage"> | string | null
    createdAt?: DateTimeFilter<"Storage"> | Date | string
    updatedAt?: DateTimeFilter<"Storage"> | Date | string
    users?: UserListRelationFilter
    productOrders?: ProductOrderListRelationFilter
  }, "id" | "code">

  export type StorageOrderByWithAggregationInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    otp?: SortOrder
    code?: SortOrder
    status?: SortOrder
    message?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: StorageCountOrderByAggregateInput
    _avg?: StorageAvgOrderByAggregateInput
    _max?: StorageMaxOrderByAggregateInput
    _min?: StorageMinOrderByAggregateInput
    _sum?: StorageSumOrderByAggregateInput
  }

  export type StorageScalarWhereWithAggregatesInput = {
    AND?: StorageScalarWhereWithAggregatesInput | StorageScalarWhereWithAggregatesInput[]
    OR?: StorageScalarWhereWithAggregatesInput[]
    NOT?: StorageScalarWhereWithAggregatesInput | StorageScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"Storage"> | number
    username?: StringWithAggregatesFilter<"Storage"> | string
    password?: StringWithAggregatesFilter<"Storage"> | string
    otp?: StringWithAggregatesFilter<"Storage"> | string
    code?: StringWithAggregatesFilter<"Storage"> | string
    status?: StringWithAggregatesFilter<"Storage"> | string
    message?: StringNullableWithAggregatesFilter<"Storage"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"Storage"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Storage"> | Date | string
  }

  export type ProductOrderWhereInput = {
    AND?: ProductOrderWhereInput | ProductOrderWhereInput[]
    OR?: ProductOrderWhereInput[]
    NOT?: ProductOrderWhereInput | ProductOrderWhereInput[]
    id?: StringFilter<"ProductOrder"> | string
    name?: StringFilter<"ProductOrder"> | string
    status?: IntFilter<"ProductOrder"> | number
    tariffId?: StringFilter<"ProductOrder"> | string
    orderInfoId?: StringFilter<"ProductOrder"> | string
    tariffName?: StringFilter<"ProductOrder"> | string
    pckCode?: StringFilter<"ProductOrder"> | string
    orderDate?: DateTimeFilter<"ProductOrder"> | Date | string
    storageId?: IntFilter<"ProductOrder"> | number
    createdAt?: DateTimeFilter<"ProductOrder"> | Date | string
    updatedAt?: DateTimeFilter<"ProductOrder"> | Date | string
    storage?: XOR<StorageScalarRelationFilter, StorageWhereInput>
    phoneItems?: PhoneItemListRelationFilter
    assignLogs?: AssignLogListRelationFilter
  }

  export type ProductOrderOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    tariffId?: SortOrder
    orderInfoId?: SortOrder
    tariffName?: SortOrder
    pckCode?: SortOrder
    orderDate?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    storage?: StorageOrderByWithRelationInput
    phoneItems?: PhoneItemOrderByRelationAggregateInput
    assignLogs?: AssignLogOrderByRelationAggregateInput
  }

  export type ProductOrderWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: ProductOrderWhereInput | ProductOrderWhereInput[]
    OR?: ProductOrderWhereInput[]
    NOT?: ProductOrderWhereInput | ProductOrderWhereInput[]
    name?: StringFilter<"ProductOrder"> | string
    status?: IntFilter<"ProductOrder"> | number
    tariffId?: StringFilter<"ProductOrder"> | string
    orderInfoId?: StringFilter<"ProductOrder"> | string
    tariffName?: StringFilter<"ProductOrder"> | string
    pckCode?: StringFilter<"ProductOrder"> | string
    orderDate?: DateTimeFilter<"ProductOrder"> | Date | string
    storageId?: IntFilter<"ProductOrder"> | number
    createdAt?: DateTimeFilter<"ProductOrder"> | Date | string
    updatedAt?: DateTimeFilter<"ProductOrder"> | Date | string
    storage?: XOR<StorageScalarRelationFilter, StorageWhereInput>
    phoneItems?: PhoneItemListRelationFilter
    assignLogs?: AssignLogListRelationFilter
  }, "id">

  export type ProductOrderOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    tariffId?: SortOrder
    orderInfoId?: SortOrder
    tariffName?: SortOrder
    pckCode?: SortOrder
    orderDate?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: ProductOrderCountOrderByAggregateInput
    _avg?: ProductOrderAvgOrderByAggregateInput
    _max?: ProductOrderMaxOrderByAggregateInput
    _min?: ProductOrderMinOrderByAggregateInput
    _sum?: ProductOrderSumOrderByAggregateInput
  }

  export type ProductOrderScalarWhereWithAggregatesInput = {
    AND?: ProductOrderScalarWhereWithAggregatesInput | ProductOrderScalarWhereWithAggregatesInput[]
    OR?: ProductOrderScalarWhereWithAggregatesInput[]
    NOT?: ProductOrderScalarWhereWithAggregatesInput | ProductOrderScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"ProductOrder"> | string
    name?: StringWithAggregatesFilter<"ProductOrder"> | string
    status?: IntWithAggregatesFilter<"ProductOrder"> | number
    tariffId?: StringWithAggregatesFilter<"ProductOrder"> | string
    orderInfoId?: StringWithAggregatesFilter<"ProductOrder"> | string
    tariffName?: StringWithAggregatesFilter<"ProductOrder"> | string
    pckCode?: StringWithAggregatesFilter<"ProductOrder"> | string
    orderDate?: DateTimeWithAggregatesFilter<"ProductOrder"> | Date | string
    storageId?: IntWithAggregatesFilter<"ProductOrder"> | number
    createdAt?: DateTimeWithAggregatesFilter<"ProductOrder"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"ProductOrder"> | Date | string
  }

  export type PhoneItemWhereInput = {
    AND?: PhoneItemWhereInput | PhoneItemWhereInput[]
    OR?: PhoneItemWhereInput[]
    NOT?: PhoneItemWhereInput | PhoneItemWhereInput[]
    id?: StringFilter<"PhoneItem"> | string
    phoneNumber?: StringFilter<"PhoneItem"> | string
    status?: IntFilter<"PhoneItem"> | number
    addedDate?: DateTimeNullableFilter<"PhoneItem"> | Date | string | null
    tariffCode?: StringNullableFilter<"PhoneItem"> | string | null
    productOrderId?: StringFilter<"PhoneItem"> | string
    createdAt?: DateTimeFilter<"PhoneItem"> | Date | string
    updatedAt?: DateTimeFilter<"PhoneItem"> | Date | string
    productOrder?: XOR<ProductOrderScalarRelationFilter, ProductOrderWhereInput>
  }

  export type PhoneItemOrderByWithRelationInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    addedDate?: SortOrderInput | SortOrder
    tariffCode?: SortOrderInput | SortOrder
    productOrderId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    productOrder?: ProductOrderOrderByWithRelationInput
  }

  export type PhoneItemWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: PhoneItemWhereInput | PhoneItemWhereInput[]
    OR?: PhoneItemWhereInput[]
    NOT?: PhoneItemWhereInput | PhoneItemWhereInput[]
    phoneNumber?: StringFilter<"PhoneItem"> | string
    status?: IntFilter<"PhoneItem"> | number
    addedDate?: DateTimeNullableFilter<"PhoneItem"> | Date | string | null
    tariffCode?: StringNullableFilter<"PhoneItem"> | string | null
    productOrderId?: StringFilter<"PhoneItem"> | string
    createdAt?: DateTimeFilter<"PhoneItem"> | Date | string
    updatedAt?: DateTimeFilter<"PhoneItem"> | Date | string
    productOrder?: XOR<ProductOrderScalarRelationFilter, ProductOrderWhereInput>
  }, "id">

  export type PhoneItemOrderByWithAggregationInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    addedDate?: SortOrderInput | SortOrder
    tariffCode?: SortOrderInput | SortOrder
    productOrderId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: PhoneItemCountOrderByAggregateInput
    _avg?: PhoneItemAvgOrderByAggregateInput
    _max?: PhoneItemMaxOrderByAggregateInput
    _min?: PhoneItemMinOrderByAggregateInput
    _sum?: PhoneItemSumOrderByAggregateInput
  }

  export type PhoneItemScalarWhereWithAggregatesInput = {
    AND?: PhoneItemScalarWhereWithAggregatesInput | PhoneItemScalarWhereWithAggregatesInput[]
    OR?: PhoneItemScalarWhereWithAggregatesInput[]
    NOT?: PhoneItemScalarWhereWithAggregatesInput | PhoneItemScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"PhoneItem"> | string
    phoneNumber?: StringWithAggregatesFilter<"PhoneItem"> | string
    status?: IntWithAggregatesFilter<"PhoneItem"> | number
    addedDate?: DateTimeNullableWithAggregatesFilter<"PhoneItem"> | Date | string | null
    tariffCode?: StringNullableWithAggregatesFilter<"PhoneItem"> | string | null
    productOrderId?: StringWithAggregatesFilter<"PhoneItem"> | string
    createdAt?: DateTimeWithAggregatesFilter<"PhoneItem"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"PhoneItem"> | Date | string
  }

  export type AssignLogWhereInput = {
    AND?: AssignLogWhereInput | AssignLogWhereInput[]
    OR?: AssignLogWhereInput[]
    NOT?: AssignLogWhereInput | AssignLogWhereInput[]
    id?: IntFilter<"AssignLog"> | number
    phoneNumber?: StringFilter<"AssignLog"> | string
    status?: StringFilter<"AssignLog"> | string
    message?: StringNullableFilter<"AssignLog"> | string | null
    productOrderId?: StringFilter<"AssignLog"> | string
    userId?: IntFilter<"AssignLog"> | number
    createdAt?: DateTimeFilter<"AssignLog"> | Date | string
    updatedAt?: DateTimeFilter<"AssignLog"> | Date | string
    productOrder?: XOR<ProductOrderScalarRelationFilter, ProductOrderWhereInput>
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }

  export type AssignLogOrderByWithRelationInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    message?: SortOrderInput | SortOrder
    productOrderId?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    productOrder?: ProductOrderOrderByWithRelationInput
    user?: UserOrderByWithRelationInput
  }

  export type AssignLogWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    AND?: AssignLogWhereInput | AssignLogWhereInput[]
    OR?: AssignLogWhereInput[]
    NOT?: AssignLogWhereInput | AssignLogWhereInput[]
    phoneNumber?: StringFilter<"AssignLog"> | string
    status?: StringFilter<"AssignLog"> | string
    message?: StringNullableFilter<"AssignLog"> | string | null
    productOrderId?: StringFilter<"AssignLog"> | string
    userId?: IntFilter<"AssignLog"> | number
    createdAt?: DateTimeFilter<"AssignLog"> | Date | string
    updatedAt?: DateTimeFilter<"AssignLog"> | Date | string
    productOrder?: XOR<ProductOrderScalarRelationFilter, ProductOrderWhereInput>
    user?: XOR<UserScalarRelationFilter, UserWhereInput>
  }, "id">

  export type AssignLogOrderByWithAggregationInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    message?: SortOrderInput | SortOrder
    productOrderId?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: AssignLogCountOrderByAggregateInput
    _avg?: AssignLogAvgOrderByAggregateInput
    _max?: AssignLogMaxOrderByAggregateInput
    _min?: AssignLogMinOrderByAggregateInput
    _sum?: AssignLogSumOrderByAggregateInput
  }

  export type AssignLogScalarWhereWithAggregatesInput = {
    AND?: AssignLogScalarWhereWithAggregatesInput | AssignLogScalarWhereWithAggregatesInput[]
    OR?: AssignLogScalarWhereWithAggregatesInput[]
    NOT?: AssignLogScalarWhereWithAggregatesInput | AssignLogScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"AssignLog"> | number
    phoneNumber?: StringWithAggregatesFilter<"AssignLog"> | string
    status?: StringWithAggregatesFilter<"AssignLog"> | string
    message?: StringNullableWithAggregatesFilter<"AssignLog"> | string | null
    productOrderId?: StringWithAggregatesFilter<"AssignLog"> | string
    userId?: IntWithAggregatesFilter<"AssignLog"> | number
    createdAt?: DateTimeWithAggregatesFilter<"AssignLog"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"AssignLog"> | Date | string
  }

  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: IntFilter<"User"> | number
    phoneNumber?: StringFilter<"User"> | string
    uid?: StringNullableFilter<"User"> | string | null
    name?: StringNullableFilter<"User"> | string | null
    status?: StringFilter<"User"> | string
    storageId?: IntFilter<"User"> | number
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    storage?: XOR<StorageScalarRelationFilter, StorageWhereInput>
    assignLogs?: AssignLogListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    uid?: SortOrderInput | SortOrder
    name?: SortOrderInput | SortOrder
    status?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    storage?: StorageOrderByWithRelationInput
    assignLogs?: AssignLogOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    phoneNumber_storageId?: UserPhoneNumberStorageIdCompoundUniqueInput
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    phoneNumber?: StringFilter<"User"> | string
    uid?: StringNullableFilter<"User"> | string | null
    name?: StringNullableFilter<"User"> | string | null
    status?: StringFilter<"User"> | string
    storageId?: IntFilter<"User"> | number
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    storage?: XOR<StorageScalarRelationFilter, StorageWhereInput>
    assignLogs?: AssignLogListRelationFilter
  }, "id" | "phoneNumber_storageId">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    uid?: SortOrderInput | SortOrder
    name?: SortOrderInput | SortOrder
    status?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _avg?: UserAvgOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
    _sum?: UserSumOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"User"> | number
    phoneNumber?: StringWithAggregatesFilter<"User"> | string
    uid?: StringNullableWithAggregatesFilter<"User"> | string | null
    name?: StringNullableWithAggregatesFilter<"User"> | string | null
    status?: StringWithAggregatesFilter<"User"> | string
    storageId?: IntWithAggregatesFilter<"User"> | number
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
  }

  export type TaskWhereInput = {
    AND?: TaskWhereInput | TaskWhereInput[]
    OR?: TaskWhereInput[]
    NOT?: TaskWhereInput | TaskWhereInput[]
    id?: StringFilter<"Task"> | string
    type?: StringFilter<"Task"> | string
    status?: StringFilter<"Task"> | string
    data?: JsonFilter<"Task">
    result?: JsonNullableFilter<"Task">
    message?: StringNullableFilter<"Task"> | string | null
    createdAt?: DateTimeFilter<"Task"> | Date | string
    updatedAt?: DateTimeFilter<"Task"> | Date | string
  }

  export type TaskOrderByWithRelationInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    data?: SortOrder
    result?: SortOrderInput | SortOrder
    message?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TaskWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: TaskWhereInput | TaskWhereInput[]
    OR?: TaskWhereInput[]
    NOT?: TaskWhereInput | TaskWhereInput[]
    type?: StringFilter<"Task"> | string
    status?: StringFilter<"Task"> | string
    data?: JsonFilter<"Task">
    result?: JsonNullableFilter<"Task">
    message?: StringNullableFilter<"Task"> | string | null
    createdAt?: DateTimeFilter<"Task"> | Date | string
    updatedAt?: DateTimeFilter<"Task"> | Date | string
  }, "id">

  export type TaskOrderByWithAggregationInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    data?: SortOrder
    result?: SortOrderInput | SortOrder
    message?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    _count?: TaskCountOrderByAggregateInput
    _max?: TaskMaxOrderByAggregateInput
    _min?: TaskMinOrderByAggregateInput
  }

  export type TaskScalarWhereWithAggregatesInput = {
    AND?: TaskScalarWhereWithAggregatesInput | TaskScalarWhereWithAggregatesInput[]
    OR?: TaskScalarWhereWithAggregatesInput[]
    NOT?: TaskScalarWhereWithAggregatesInput | TaskScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Task"> | string
    type?: StringWithAggregatesFilter<"Task"> | string
    status?: StringWithAggregatesFilter<"Task"> | string
    data?: JsonWithAggregatesFilter<"Task">
    result?: JsonNullableWithAggregatesFilter<"Task">
    message?: StringNullableWithAggregatesFilter<"Task"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"Task"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"Task"> | Date | string
  }

  export type StorageCreateInput = {
    username: string
    password: string
    otp: string
    code: string
    status?: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    users?: UserCreateNestedManyWithoutStorageInput
    productOrders?: ProductOrderCreateNestedManyWithoutStorageInput
  }

  export type StorageUncheckedCreateInput = {
    id?: number
    username: string
    password: string
    otp: string
    code: string
    status?: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    users?: UserUncheckedCreateNestedManyWithoutStorageInput
    productOrders?: ProductOrderUncheckedCreateNestedManyWithoutStorageInput
  }

  export type StorageUpdateInput = {
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    otp?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    users?: UserUpdateManyWithoutStorageNestedInput
    productOrders?: ProductOrderUpdateManyWithoutStorageNestedInput
  }

  export type StorageUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    otp?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    users?: UserUncheckedUpdateManyWithoutStorageNestedInput
    productOrders?: ProductOrderUncheckedUpdateManyWithoutStorageNestedInput
  }

  export type StorageCreateManyInput = {
    id?: number
    username: string
    password: string
    otp: string
    code: string
    status?: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type StorageUpdateManyMutationInput = {
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    otp?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StorageUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    otp?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductOrderCreateInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
    storage: StorageCreateNestedOneWithoutProductOrdersInput
    phoneItems?: PhoneItemCreateNestedManyWithoutProductOrderInput
    assignLogs?: AssignLogCreateNestedManyWithoutProductOrderInput
  }

  export type ProductOrderUncheckedCreateInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    storageId: number
    createdAt?: Date | string
    updatedAt?: Date | string
    phoneItems?: PhoneItemUncheckedCreateNestedManyWithoutProductOrderInput
    assignLogs?: AssignLogUncheckedCreateNestedManyWithoutProductOrderInput
  }

  export type ProductOrderUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    storage?: StorageUpdateOneRequiredWithoutProductOrdersNestedInput
    phoneItems?: PhoneItemUpdateManyWithoutProductOrderNestedInput
    assignLogs?: AssignLogUpdateManyWithoutProductOrderNestedInput
  }

  export type ProductOrderUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    storageId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    phoneItems?: PhoneItemUncheckedUpdateManyWithoutProductOrderNestedInput
    assignLogs?: AssignLogUncheckedUpdateManyWithoutProductOrderNestedInput
  }

  export type ProductOrderCreateManyInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    storageId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductOrderUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductOrderUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    storageId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PhoneItemCreateInput = {
    id: string
    phoneNumber: string
    status: number
    addedDate?: Date | string | null
    tariffCode?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    productOrder: ProductOrderCreateNestedOneWithoutPhoneItemsInput
  }

  export type PhoneItemUncheckedCreateInput = {
    id: string
    phoneNumber: string
    status: number
    addedDate?: Date | string | null
    tariffCode?: string | null
    productOrderId: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PhoneItemUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    addedDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tariffCode?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    productOrder?: ProductOrderUpdateOneRequiredWithoutPhoneItemsNestedInput
  }

  export type PhoneItemUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    addedDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tariffCode?: NullableStringFieldUpdateOperationsInput | string | null
    productOrderId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PhoneItemCreateManyInput = {
    id: string
    phoneNumber: string
    status: number
    addedDate?: Date | string | null
    tariffCode?: string | null
    productOrderId: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PhoneItemUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    addedDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tariffCode?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PhoneItemUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    addedDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tariffCode?: NullableStringFieldUpdateOperationsInput | string | null
    productOrderId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignLogCreateInput = {
    phoneNumber: string
    status: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    productOrder: ProductOrderCreateNestedOneWithoutAssignLogsInput
    user: UserCreateNestedOneWithoutAssignLogsInput
  }

  export type AssignLogUncheckedCreateInput = {
    id?: number
    phoneNumber: string
    status: string
    message?: string | null
    productOrderId: string
    userId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AssignLogUpdateInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    productOrder?: ProductOrderUpdateOneRequiredWithoutAssignLogsNestedInput
    user?: UserUpdateOneRequiredWithoutAssignLogsNestedInput
  }

  export type AssignLogUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    productOrderId?: StringFieldUpdateOperationsInput | string
    userId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignLogCreateManyInput = {
    id?: number
    phoneNumber: string
    status: string
    message?: string | null
    productOrderId: string
    userId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AssignLogUpdateManyMutationInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignLogUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    productOrderId?: StringFieldUpdateOperationsInput | string
    userId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserCreateInput = {
    phoneNumber: string
    uid?: string | null
    name?: string | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    storage: StorageCreateNestedOneWithoutUsersInput
    assignLogs?: AssignLogCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: number
    phoneNumber: string
    uid?: string | null
    name?: string | null
    status?: string
    storageId: number
    createdAt?: Date | string
    updatedAt?: Date | string
    assignLogs?: AssignLogUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    storage?: StorageUpdateOneRequiredWithoutUsersNestedInput
    assignLogs?: AssignLogUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    storageId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    assignLogs?: AssignLogUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: number
    phoneNumber: string
    uid?: string | null
    name?: string | null
    status?: string
    storageId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateManyMutationInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type UserUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    storageId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TaskCreateInput = {
    id?: string
    type: string
    status?: string
    data: JsonNullValueInput | InputJsonValue
    result?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TaskUncheckedCreateInput = {
    id?: string
    type: string
    status?: string
    data: JsonNullValueInput | InputJsonValue
    result?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TaskUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    data?: JsonNullValueInput | InputJsonValue
    result?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TaskUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    data?: JsonNullValueInput | InputJsonValue
    result?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TaskCreateManyInput = {
    id?: string
    type: string
    status?: string
    data: JsonNullValueInput | InputJsonValue
    result?: NullableJsonNullValueInput | InputJsonValue
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type TaskUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    data?: JsonNullValueInput | InputJsonValue
    result?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type TaskUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    type?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    data?: JsonNullValueInput | InputJsonValue
    result?: NullableJsonNullValueInput | InputJsonValue
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type UserListRelationFilter = {
    every?: UserWhereInput
    some?: UserWhereInput
    none?: UserWhereInput
  }

  export type ProductOrderListRelationFilter = {
    every?: ProductOrderWhereInput
    some?: ProductOrderWhereInput
    none?: ProductOrderWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type UserOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type ProductOrderOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type StorageCountOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    otp?: SortOrder
    code?: SortOrder
    status?: SortOrder
    message?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StorageAvgOrderByAggregateInput = {
    id?: SortOrder
  }

  export type StorageMaxOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    otp?: SortOrder
    code?: SortOrder
    status?: SortOrder
    message?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StorageMinOrderByAggregateInput = {
    id?: SortOrder
    username?: SortOrder
    password?: SortOrder
    otp?: SortOrder
    code?: SortOrder
    status?: SortOrder
    message?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type StorageSumOrderByAggregateInput = {
    id?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type StorageScalarRelationFilter = {
    is?: StorageWhereInput
    isNot?: StorageWhereInput
  }

  export type PhoneItemListRelationFilter = {
    every?: PhoneItemWhereInput
    some?: PhoneItemWhereInput
    none?: PhoneItemWhereInput
  }

  export type AssignLogListRelationFilter = {
    every?: AssignLogWhereInput
    some?: AssignLogWhereInput
    none?: AssignLogWhereInput
  }

  export type PhoneItemOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type AssignLogOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type ProductOrderCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    tariffId?: SortOrder
    orderInfoId?: SortOrder
    tariffName?: SortOrder
    pckCode?: SortOrder
    orderDate?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductOrderAvgOrderByAggregateInput = {
    status?: SortOrder
    storageId?: SortOrder
  }

  export type ProductOrderMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    tariffId?: SortOrder
    orderInfoId?: SortOrder
    tariffName?: SortOrder
    pckCode?: SortOrder
    orderDate?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductOrderMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    status?: SortOrder
    tariffId?: SortOrder
    orderInfoId?: SortOrder
    tariffName?: SortOrder
    pckCode?: SortOrder
    orderDate?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type ProductOrderSumOrderByAggregateInput = {
    status?: SortOrder
    storageId?: SortOrder
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type ProductOrderScalarRelationFilter = {
    is?: ProductOrderWhereInput
    isNot?: ProductOrderWhereInput
  }

  export type PhoneItemCountOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    addedDate?: SortOrder
    tariffCode?: SortOrder
    productOrderId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type PhoneItemAvgOrderByAggregateInput = {
    status?: SortOrder
  }

  export type PhoneItemMaxOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    addedDate?: SortOrder
    tariffCode?: SortOrder
    productOrderId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type PhoneItemMinOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    addedDate?: SortOrder
    tariffCode?: SortOrder
    productOrderId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type PhoneItemSumOrderByAggregateInput = {
    status?: SortOrder
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type UserScalarRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type AssignLogCountOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    message?: SortOrder
    productOrderId?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AssignLogAvgOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
  }

  export type AssignLogMaxOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    message?: SortOrder
    productOrderId?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AssignLogMinOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    status?: SortOrder
    message?: SortOrder
    productOrderId?: SortOrder
    userId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type AssignLogSumOrderByAggregateInput = {
    id?: SortOrder
    userId?: SortOrder
  }

  export type UserPhoneNumberStorageIdCompoundUniqueInput = {
    phoneNumber: string
    storageId: number
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    uid?: SortOrder
    name?: SortOrder
    status?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserAvgOrderByAggregateInput = {
    id?: SortOrder
    storageId?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    uid?: SortOrder
    name?: SortOrder
    status?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    phoneNumber?: SortOrder
    uid?: SortOrder
    name?: SortOrder
    status?: SortOrder
    storageId?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type UserSumOrderByAggregateInput = {
    id?: SortOrder
    storageId?: SortOrder
  }
  export type JsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }
  export type JsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type TaskCountOrderByAggregateInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    data?: SortOrder
    result?: SortOrder
    message?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TaskMaxOrderByAggregateInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    message?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type TaskMinOrderByAggregateInput = {
    id?: SortOrder
    type?: SortOrder
    status?: SortOrder
    message?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }
  export type JsonWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedJsonFilter<$PrismaModel>
    _max?: NestedJsonFilter<$PrismaModel>
  }
  export type JsonNullableWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonNullableWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonNullableWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedJsonNullableFilter<$PrismaModel>
    _max?: NestedJsonNullableFilter<$PrismaModel>
  }

  export type UserCreateNestedManyWithoutStorageInput = {
    create?: XOR<UserCreateWithoutStorageInput, UserUncheckedCreateWithoutStorageInput> | UserCreateWithoutStorageInput[] | UserUncheckedCreateWithoutStorageInput[]
    connectOrCreate?: UserCreateOrConnectWithoutStorageInput | UserCreateOrConnectWithoutStorageInput[]
    createMany?: UserCreateManyStorageInputEnvelope
    connect?: UserWhereUniqueInput | UserWhereUniqueInput[]
  }

  export type ProductOrderCreateNestedManyWithoutStorageInput = {
    create?: XOR<ProductOrderCreateWithoutStorageInput, ProductOrderUncheckedCreateWithoutStorageInput> | ProductOrderCreateWithoutStorageInput[] | ProductOrderUncheckedCreateWithoutStorageInput[]
    connectOrCreate?: ProductOrderCreateOrConnectWithoutStorageInput | ProductOrderCreateOrConnectWithoutStorageInput[]
    createMany?: ProductOrderCreateManyStorageInputEnvelope
    connect?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
  }

  export type UserUncheckedCreateNestedManyWithoutStorageInput = {
    create?: XOR<UserCreateWithoutStorageInput, UserUncheckedCreateWithoutStorageInput> | UserCreateWithoutStorageInput[] | UserUncheckedCreateWithoutStorageInput[]
    connectOrCreate?: UserCreateOrConnectWithoutStorageInput | UserCreateOrConnectWithoutStorageInput[]
    createMany?: UserCreateManyStorageInputEnvelope
    connect?: UserWhereUniqueInput | UserWhereUniqueInput[]
  }

  export type ProductOrderUncheckedCreateNestedManyWithoutStorageInput = {
    create?: XOR<ProductOrderCreateWithoutStorageInput, ProductOrderUncheckedCreateWithoutStorageInput> | ProductOrderCreateWithoutStorageInput[] | ProductOrderUncheckedCreateWithoutStorageInput[]
    connectOrCreate?: ProductOrderCreateOrConnectWithoutStorageInput | ProductOrderCreateOrConnectWithoutStorageInput[]
    createMany?: ProductOrderCreateManyStorageInputEnvelope
    connect?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type UserUpdateManyWithoutStorageNestedInput = {
    create?: XOR<UserCreateWithoutStorageInput, UserUncheckedCreateWithoutStorageInput> | UserCreateWithoutStorageInput[] | UserUncheckedCreateWithoutStorageInput[]
    connectOrCreate?: UserCreateOrConnectWithoutStorageInput | UserCreateOrConnectWithoutStorageInput[]
    upsert?: UserUpsertWithWhereUniqueWithoutStorageInput | UserUpsertWithWhereUniqueWithoutStorageInput[]
    createMany?: UserCreateManyStorageInputEnvelope
    set?: UserWhereUniqueInput | UserWhereUniqueInput[]
    disconnect?: UserWhereUniqueInput | UserWhereUniqueInput[]
    delete?: UserWhereUniqueInput | UserWhereUniqueInput[]
    connect?: UserWhereUniqueInput | UserWhereUniqueInput[]
    update?: UserUpdateWithWhereUniqueWithoutStorageInput | UserUpdateWithWhereUniqueWithoutStorageInput[]
    updateMany?: UserUpdateManyWithWhereWithoutStorageInput | UserUpdateManyWithWhereWithoutStorageInput[]
    deleteMany?: UserScalarWhereInput | UserScalarWhereInput[]
  }

  export type ProductOrderUpdateManyWithoutStorageNestedInput = {
    create?: XOR<ProductOrderCreateWithoutStorageInput, ProductOrderUncheckedCreateWithoutStorageInput> | ProductOrderCreateWithoutStorageInput[] | ProductOrderUncheckedCreateWithoutStorageInput[]
    connectOrCreate?: ProductOrderCreateOrConnectWithoutStorageInput | ProductOrderCreateOrConnectWithoutStorageInput[]
    upsert?: ProductOrderUpsertWithWhereUniqueWithoutStorageInput | ProductOrderUpsertWithWhereUniqueWithoutStorageInput[]
    createMany?: ProductOrderCreateManyStorageInputEnvelope
    set?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
    disconnect?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
    delete?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
    connect?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
    update?: ProductOrderUpdateWithWhereUniqueWithoutStorageInput | ProductOrderUpdateWithWhereUniqueWithoutStorageInput[]
    updateMany?: ProductOrderUpdateManyWithWhereWithoutStorageInput | ProductOrderUpdateManyWithWhereWithoutStorageInput[]
    deleteMany?: ProductOrderScalarWhereInput | ProductOrderScalarWhereInput[]
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type UserUncheckedUpdateManyWithoutStorageNestedInput = {
    create?: XOR<UserCreateWithoutStorageInput, UserUncheckedCreateWithoutStorageInput> | UserCreateWithoutStorageInput[] | UserUncheckedCreateWithoutStorageInput[]
    connectOrCreate?: UserCreateOrConnectWithoutStorageInput | UserCreateOrConnectWithoutStorageInput[]
    upsert?: UserUpsertWithWhereUniqueWithoutStorageInput | UserUpsertWithWhereUniqueWithoutStorageInput[]
    createMany?: UserCreateManyStorageInputEnvelope
    set?: UserWhereUniqueInput | UserWhereUniqueInput[]
    disconnect?: UserWhereUniqueInput | UserWhereUniqueInput[]
    delete?: UserWhereUniqueInput | UserWhereUniqueInput[]
    connect?: UserWhereUniqueInput | UserWhereUniqueInput[]
    update?: UserUpdateWithWhereUniqueWithoutStorageInput | UserUpdateWithWhereUniqueWithoutStorageInput[]
    updateMany?: UserUpdateManyWithWhereWithoutStorageInput | UserUpdateManyWithWhereWithoutStorageInput[]
    deleteMany?: UserScalarWhereInput | UserScalarWhereInput[]
  }

  export type ProductOrderUncheckedUpdateManyWithoutStorageNestedInput = {
    create?: XOR<ProductOrderCreateWithoutStorageInput, ProductOrderUncheckedCreateWithoutStorageInput> | ProductOrderCreateWithoutStorageInput[] | ProductOrderUncheckedCreateWithoutStorageInput[]
    connectOrCreate?: ProductOrderCreateOrConnectWithoutStorageInput | ProductOrderCreateOrConnectWithoutStorageInput[]
    upsert?: ProductOrderUpsertWithWhereUniqueWithoutStorageInput | ProductOrderUpsertWithWhereUniqueWithoutStorageInput[]
    createMany?: ProductOrderCreateManyStorageInputEnvelope
    set?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
    disconnect?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
    delete?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
    connect?: ProductOrderWhereUniqueInput | ProductOrderWhereUniqueInput[]
    update?: ProductOrderUpdateWithWhereUniqueWithoutStorageInput | ProductOrderUpdateWithWhereUniqueWithoutStorageInput[]
    updateMany?: ProductOrderUpdateManyWithWhereWithoutStorageInput | ProductOrderUpdateManyWithWhereWithoutStorageInput[]
    deleteMany?: ProductOrderScalarWhereInput | ProductOrderScalarWhereInput[]
  }

  export type StorageCreateNestedOneWithoutProductOrdersInput = {
    create?: XOR<StorageCreateWithoutProductOrdersInput, StorageUncheckedCreateWithoutProductOrdersInput>
    connectOrCreate?: StorageCreateOrConnectWithoutProductOrdersInput
    connect?: StorageWhereUniqueInput
  }

  export type PhoneItemCreateNestedManyWithoutProductOrderInput = {
    create?: XOR<PhoneItemCreateWithoutProductOrderInput, PhoneItemUncheckedCreateWithoutProductOrderInput> | PhoneItemCreateWithoutProductOrderInput[] | PhoneItemUncheckedCreateWithoutProductOrderInput[]
    connectOrCreate?: PhoneItemCreateOrConnectWithoutProductOrderInput | PhoneItemCreateOrConnectWithoutProductOrderInput[]
    createMany?: PhoneItemCreateManyProductOrderInputEnvelope
    connect?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
  }

  export type AssignLogCreateNestedManyWithoutProductOrderInput = {
    create?: XOR<AssignLogCreateWithoutProductOrderInput, AssignLogUncheckedCreateWithoutProductOrderInput> | AssignLogCreateWithoutProductOrderInput[] | AssignLogUncheckedCreateWithoutProductOrderInput[]
    connectOrCreate?: AssignLogCreateOrConnectWithoutProductOrderInput | AssignLogCreateOrConnectWithoutProductOrderInput[]
    createMany?: AssignLogCreateManyProductOrderInputEnvelope
    connect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
  }

  export type PhoneItemUncheckedCreateNestedManyWithoutProductOrderInput = {
    create?: XOR<PhoneItemCreateWithoutProductOrderInput, PhoneItemUncheckedCreateWithoutProductOrderInput> | PhoneItemCreateWithoutProductOrderInput[] | PhoneItemUncheckedCreateWithoutProductOrderInput[]
    connectOrCreate?: PhoneItemCreateOrConnectWithoutProductOrderInput | PhoneItemCreateOrConnectWithoutProductOrderInput[]
    createMany?: PhoneItemCreateManyProductOrderInputEnvelope
    connect?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
  }

  export type AssignLogUncheckedCreateNestedManyWithoutProductOrderInput = {
    create?: XOR<AssignLogCreateWithoutProductOrderInput, AssignLogUncheckedCreateWithoutProductOrderInput> | AssignLogCreateWithoutProductOrderInput[] | AssignLogUncheckedCreateWithoutProductOrderInput[]
    connectOrCreate?: AssignLogCreateOrConnectWithoutProductOrderInput | AssignLogCreateOrConnectWithoutProductOrderInput[]
    createMany?: AssignLogCreateManyProductOrderInputEnvelope
    connect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
  }

  export type StorageUpdateOneRequiredWithoutProductOrdersNestedInput = {
    create?: XOR<StorageCreateWithoutProductOrdersInput, StorageUncheckedCreateWithoutProductOrdersInput>
    connectOrCreate?: StorageCreateOrConnectWithoutProductOrdersInput
    upsert?: StorageUpsertWithoutProductOrdersInput
    connect?: StorageWhereUniqueInput
    update?: XOR<XOR<StorageUpdateToOneWithWhereWithoutProductOrdersInput, StorageUpdateWithoutProductOrdersInput>, StorageUncheckedUpdateWithoutProductOrdersInput>
  }

  export type PhoneItemUpdateManyWithoutProductOrderNestedInput = {
    create?: XOR<PhoneItemCreateWithoutProductOrderInput, PhoneItemUncheckedCreateWithoutProductOrderInput> | PhoneItemCreateWithoutProductOrderInput[] | PhoneItemUncheckedCreateWithoutProductOrderInput[]
    connectOrCreate?: PhoneItemCreateOrConnectWithoutProductOrderInput | PhoneItemCreateOrConnectWithoutProductOrderInput[]
    upsert?: PhoneItemUpsertWithWhereUniqueWithoutProductOrderInput | PhoneItemUpsertWithWhereUniqueWithoutProductOrderInput[]
    createMany?: PhoneItemCreateManyProductOrderInputEnvelope
    set?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
    disconnect?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
    delete?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
    connect?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
    update?: PhoneItemUpdateWithWhereUniqueWithoutProductOrderInput | PhoneItemUpdateWithWhereUniqueWithoutProductOrderInput[]
    updateMany?: PhoneItemUpdateManyWithWhereWithoutProductOrderInput | PhoneItemUpdateManyWithWhereWithoutProductOrderInput[]
    deleteMany?: PhoneItemScalarWhereInput | PhoneItemScalarWhereInput[]
  }

  export type AssignLogUpdateManyWithoutProductOrderNestedInput = {
    create?: XOR<AssignLogCreateWithoutProductOrderInput, AssignLogUncheckedCreateWithoutProductOrderInput> | AssignLogCreateWithoutProductOrderInput[] | AssignLogUncheckedCreateWithoutProductOrderInput[]
    connectOrCreate?: AssignLogCreateOrConnectWithoutProductOrderInput | AssignLogCreateOrConnectWithoutProductOrderInput[]
    upsert?: AssignLogUpsertWithWhereUniqueWithoutProductOrderInput | AssignLogUpsertWithWhereUniqueWithoutProductOrderInput[]
    createMany?: AssignLogCreateManyProductOrderInputEnvelope
    set?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    disconnect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    delete?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    connect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    update?: AssignLogUpdateWithWhereUniqueWithoutProductOrderInput | AssignLogUpdateWithWhereUniqueWithoutProductOrderInput[]
    updateMany?: AssignLogUpdateManyWithWhereWithoutProductOrderInput | AssignLogUpdateManyWithWhereWithoutProductOrderInput[]
    deleteMany?: AssignLogScalarWhereInput | AssignLogScalarWhereInput[]
  }

  export type PhoneItemUncheckedUpdateManyWithoutProductOrderNestedInput = {
    create?: XOR<PhoneItemCreateWithoutProductOrderInput, PhoneItemUncheckedCreateWithoutProductOrderInput> | PhoneItemCreateWithoutProductOrderInput[] | PhoneItemUncheckedCreateWithoutProductOrderInput[]
    connectOrCreate?: PhoneItemCreateOrConnectWithoutProductOrderInput | PhoneItemCreateOrConnectWithoutProductOrderInput[]
    upsert?: PhoneItemUpsertWithWhereUniqueWithoutProductOrderInput | PhoneItemUpsertWithWhereUniqueWithoutProductOrderInput[]
    createMany?: PhoneItemCreateManyProductOrderInputEnvelope
    set?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
    disconnect?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
    delete?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
    connect?: PhoneItemWhereUniqueInput | PhoneItemWhereUniqueInput[]
    update?: PhoneItemUpdateWithWhereUniqueWithoutProductOrderInput | PhoneItemUpdateWithWhereUniqueWithoutProductOrderInput[]
    updateMany?: PhoneItemUpdateManyWithWhereWithoutProductOrderInput | PhoneItemUpdateManyWithWhereWithoutProductOrderInput[]
    deleteMany?: PhoneItemScalarWhereInput | PhoneItemScalarWhereInput[]
  }

  export type AssignLogUncheckedUpdateManyWithoutProductOrderNestedInput = {
    create?: XOR<AssignLogCreateWithoutProductOrderInput, AssignLogUncheckedCreateWithoutProductOrderInput> | AssignLogCreateWithoutProductOrderInput[] | AssignLogUncheckedCreateWithoutProductOrderInput[]
    connectOrCreate?: AssignLogCreateOrConnectWithoutProductOrderInput | AssignLogCreateOrConnectWithoutProductOrderInput[]
    upsert?: AssignLogUpsertWithWhereUniqueWithoutProductOrderInput | AssignLogUpsertWithWhereUniqueWithoutProductOrderInput[]
    createMany?: AssignLogCreateManyProductOrderInputEnvelope
    set?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    disconnect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    delete?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    connect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    update?: AssignLogUpdateWithWhereUniqueWithoutProductOrderInput | AssignLogUpdateWithWhereUniqueWithoutProductOrderInput[]
    updateMany?: AssignLogUpdateManyWithWhereWithoutProductOrderInput | AssignLogUpdateManyWithWhereWithoutProductOrderInput[]
    deleteMany?: AssignLogScalarWhereInput | AssignLogScalarWhereInput[]
  }

  export type ProductOrderCreateNestedOneWithoutPhoneItemsInput = {
    create?: XOR<ProductOrderCreateWithoutPhoneItemsInput, ProductOrderUncheckedCreateWithoutPhoneItemsInput>
    connectOrCreate?: ProductOrderCreateOrConnectWithoutPhoneItemsInput
    connect?: ProductOrderWhereUniqueInput
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type ProductOrderUpdateOneRequiredWithoutPhoneItemsNestedInput = {
    create?: XOR<ProductOrderCreateWithoutPhoneItemsInput, ProductOrderUncheckedCreateWithoutPhoneItemsInput>
    connectOrCreate?: ProductOrderCreateOrConnectWithoutPhoneItemsInput
    upsert?: ProductOrderUpsertWithoutPhoneItemsInput
    connect?: ProductOrderWhereUniqueInput
    update?: XOR<XOR<ProductOrderUpdateToOneWithWhereWithoutPhoneItemsInput, ProductOrderUpdateWithoutPhoneItemsInput>, ProductOrderUncheckedUpdateWithoutPhoneItemsInput>
  }

  export type ProductOrderCreateNestedOneWithoutAssignLogsInput = {
    create?: XOR<ProductOrderCreateWithoutAssignLogsInput, ProductOrderUncheckedCreateWithoutAssignLogsInput>
    connectOrCreate?: ProductOrderCreateOrConnectWithoutAssignLogsInput
    connect?: ProductOrderWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutAssignLogsInput = {
    create?: XOR<UserCreateWithoutAssignLogsInput, UserUncheckedCreateWithoutAssignLogsInput>
    connectOrCreate?: UserCreateOrConnectWithoutAssignLogsInput
    connect?: UserWhereUniqueInput
  }

  export type ProductOrderUpdateOneRequiredWithoutAssignLogsNestedInput = {
    create?: XOR<ProductOrderCreateWithoutAssignLogsInput, ProductOrderUncheckedCreateWithoutAssignLogsInput>
    connectOrCreate?: ProductOrderCreateOrConnectWithoutAssignLogsInput
    upsert?: ProductOrderUpsertWithoutAssignLogsInput
    connect?: ProductOrderWhereUniqueInput
    update?: XOR<XOR<ProductOrderUpdateToOneWithWhereWithoutAssignLogsInput, ProductOrderUpdateWithoutAssignLogsInput>, ProductOrderUncheckedUpdateWithoutAssignLogsInput>
  }

  export type UserUpdateOneRequiredWithoutAssignLogsNestedInput = {
    create?: XOR<UserCreateWithoutAssignLogsInput, UserUncheckedCreateWithoutAssignLogsInput>
    connectOrCreate?: UserCreateOrConnectWithoutAssignLogsInput
    upsert?: UserUpsertWithoutAssignLogsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutAssignLogsInput, UserUpdateWithoutAssignLogsInput>, UserUncheckedUpdateWithoutAssignLogsInput>
  }

  export type StorageCreateNestedOneWithoutUsersInput = {
    create?: XOR<StorageCreateWithoutUsersInput, StorageUncheckedCreateWithoutUsersInput>
    connectOrCreate?: StorageCreateOrConnectWithoutUsersInput
    connect?: StorageWhereUniqueInput
  }

  export type AssignLogCreateNestedManyWithoutUserInput = {
    create?: XOR<AssignLogCreateWithoutUserInput, AssignLogUncheckedCreateWithoutUserInput> | AssignLogCreateWithoutUserInput[] | AssignLogUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AssignLogCreateOrConnectWithoutUserInput | AssignLogCreateOrConnectWithoutUserInput[]
    createMany?: AssignLogCreateManyUserInputEnvelope
    connect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
  }

  export type AssignLogUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<AssignLogCreateWithoutUserInput, AssignLogUncheckedCreateWithoutUserInput> | AssignLogCreateWithoutUserInput[] | AssignLogUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AssignLogCreateOrConnectWithoutUserInput | AssignLogCreateOrConnectWithoutUserInput[]
    createMany?: AssignLogCreateManyUserInputEnvelope
    connect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
  }

  export type StorageUpdateOneRequiredWithoutUsersNestedInput = {
    create?: XOR<StorageCreateWithoutUsersInput, StorageUncheckedCreateWithoutUsersInput>
    connectOrCreate?: StorageCreateOrConnectWithoutUsersInput
    upsert?: StorageUpsertWithoutUsersInput
    connect?: StorageWhereUniqueInput
    update?: XOR<XOR<StorageUpdateToOneWithWhereWithoutUsersInput, StorageUpdateWithoutUsersInput>, StorageUncheckedUpdateWithoutUsersInput>
  }

  export type AssignLogUpdateManyWithoutUserNestedInput = {
    create?: XOR<AssignLogCreateWithoutUserInput, AssignLogUncheckedCreateWithoutUserInput> | AssignLogCreateWithoutUserInput[] | AssignLogUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AssignLogCreateOrConnectWithoutUserInput | AssignLogCreateOrConnectWithoutUserInput[]
    upsert?: AssignLogUpsertWithWhereUniqueWithoutUserInput | AssignLogUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: AssignLogCreateManyUserInputEnvelope
    set?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    disconnect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    delete?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    connect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    update?: AssignLogUpdateWithWhereUniqueWithoutUserInput | AssignLogUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: AssignLogUpdateManyWithWhereWithoutUserInput | AssignLogUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: AssignLogScalarWhereInput | AssignLogScalarWhereInput[]
  }

  export type AssignLogUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<AssignLogCreateWithoutUserInput, AssignLogUncheckedCreateWithoutUserInput> | AssignLogCreateWithoutUserInput[] | AssignLogUncheckedCreateWithoutUserInput[]
    connectOrCreate?: AssignLogCreateOrConnectWithoutUserInput | AssignLogCreateOrConnectWithoutUserInput[]
    upsert?: AssignLogUpsertWithWhereUniqueWithoutUserInput | AssignLogUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: AssignLogCreateManyUserInputEnvelope
    set?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    disconnect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    delete?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    connect?: AssignLogWhereUniqueInput | AssignLogWhereUniqueInput[]
    update?: AssignLogUpdateWithWhereUniqueWithoutUserInput | AssignLogUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: AssignLogUpdateManyWithWhereWithoutUserInput | AssignLogUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: AssignLogScalarWhereInput | AssignLogScalarWhereInput[]
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }
  export type NestedJsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }
  export type NestedJsonNullableFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonNullableFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonNullableFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonNullableFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonNullableFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type UserCreateWithoutStorageInput = {
    phoneNumber: string
    uid?: string | null
    name?: string | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    assignLogs?: AssignLogCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutStorageInput = {
    id?: number
    phoneNumber: string
    uid?: string | null
    name?: string | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    assignLogs?: AssignLogUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutStorageInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutStorageInput, UserUncheckedCreateWithoutStorageInput>
  }

  export type UserCreateManyStorageInputEnvelope = {
    data: UserCreateManyStorageInput | UserCreateManyStorageInput[]
    skipDuplicates?: boolean
  }

  export type ProductOrderCreateWithoutStorageInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
    phoneItems?: PhoneItemCreateNestedManyWithoutProductOrderInput
    assignLogs?: AssignLogCreateNestedManyWithoutProductOrderInput
  }

  export type ProductOrderUncheckedCreateWithoutStorageInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
    phoneItems?: PhoneItemUncheckedCreateNestedManyWithoutProductOrderInput
    assignLogs?: AssignLogUncheckedCreateNestedManyWithoutProductOrderInput
  }

  export type ProductOrderCreateOrConnectWithoutStorageInput = {
    where: ProductOrderWhereUniqueInput
    create: XOR<ProductOrderCreateWithoutStorageInput, ProductOrderUncheckedCreateWithoutStorageInput>
  }

  export type ProductOrderCreateManyStorageInputEnvelope = {
    data: ProductOrderCreateManyStorageInput | ProductOrderCreateManyStorageInput[]
    skipDuplicates?: boolean
  }

  export type UserUpsertWithWhereUniqueWithoutStorageInput = {
    where: UserWhereUniqueInput
    update: XOR<UserUpdateWithoutStorageInput, UserUncheckedUpdateWithoutStorageInput>
    create: XOR<UserCreateWithoutStorageInput, UserUncheckedCreateWithoutStorageInput>
  }

  export type UserUpdateWithWhereUniqueWithoutStorageInput = {
    where: UserWhereUniqueInput
    data: XOR<UserUpdateWithoutStorageInput, UserUncheckedUpdateWithoutStorageInput>
  }

  export type UserUpdateManyWithWhereWithoutStorageInput = {
    where: UserScalarWhereInput
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyWithoutStorageInput>
  }

  export type UserScalarWhereInput = {
    AND?: UserScalarWhereInput | UserScalarWhereInput[]
    OR?: UserScalarWhereInput[]
    NOT?: UserScalarWhereInput | UserScalarWhereInput[]
    id?: IntFilter<"User"> | number
    phoneNumber?: StringFilter<"User"> | string
    uid?: StringNullableFilter<"User"> | string | null
    name?: StringNullableFilter<"User"> | string | null
    status?: StringFilter<"User"> | string
    storageId?: IntFilter<"User"> | number
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
  }

  export type ProductOrderUpsertWithWhereUniqueWithoutStorageInput = {
    where: ProductOrderWhereUniqueInput
    update: XOR<ProductOrderUpdateWithoutStorageInput, ProductOrderUncheckedUpdateWithoutStorageInput>
    create: XOR<ProductOrderCreateWithoutStorageInput, ProductOrderUncheckedCreateWithoutStorageInput>
  }

  export type ProductOrderUpdateWithWhereUniqueWithoutStorageInput = {
    where: ProductOrderWhereUniqueInput
    data: XOR<ProductOrderUpdateWithoutStorageInput, ProductOrderUncheckedUpdateWithoutStorageInput>
  }

  export type ProductOrderUpdateManyWithWhereWithoutStorageInput = {
    where: ProductOrderScalarWhereInput
    data: XOR<ProductOrderUpdateManyMutationInput, ProductOrderUncheckedUpdateManyWithoutStorageInput>
  }

  export type ProductOrderScalarWhereInput = {
    AND?: ProductOrderScalarWhereInput | ProductOrderScalarWhereInput[]
    OR?: ProductOrderScalarWhereInput[]
    NOT?: ProductOrderScalarWhereInput | ProductOrderScalarWhereInput[]
    id?: StringFilter<"ProductOrder"> | string
    name?: StringFilter<"ProductOrder"> | string
    status?: IntFilter<"ProductOrder"> | number
    tariffId?: StringFilter<"ProductOrder"> | string
    orderInfoId?: StringFilter<"ProductOrder"> | string
    tariffName?: StringFilter<"ProductOrder"> | string
    pckCode?: StringFilter<"ProductOrder"> | string
    orderDate?: DateTimeFilter<"ProductOrder"> | Date | string
    storageId?: IntFilter<"ProductOrder"> | number
    createdAt?: DateTimeFilter<"ProductOrder"> | Date | string
    updatedAt?: DateTimeFilter<"ProductOrder"> | Date | string
  }

  export type StorageCreateWithoutProductOrdersInput = {
    username: string
    password: string
    otp: string
    code: string
    status?: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    users?: UserCreateNestedManyWithoutStorageInput
  }

  export type StorageUncheckedCreateWithoutProductOrdersInput = {
    id?: number
    username: string
    password: string
    otp: string
    code: string
    status?: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    users?: UserUncheckedCreateNestedManyWithoutStorageInput
  }

  export type StorageCreateOrConnectWithoutProductOrdersInput = {
    where: StorageWhereUniqueInput
    create: XOR<StorageCreateWithoutProductOrdersInput, StorageUncheckedCreateWithoutProductOrdersInput>
  }

  export type PhoneItemCreateWithoutProductOrderInput = {
    id: string
    phoneNumber: string
    status: number
    addedDate?: Date | string | null
    tariffCode?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PhoneItemUncheckedCreateWithoutProductOrderInput = {
    id: string
    phoneNumber: string
    status: number
    addedDate?: Date | string | null
    tariffCode?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PhoneItemCreateOrConnectWithoutProductOrderInput = {
    where: PhoneItemWhereUniqueInput
    create: XOR<PhoneItemCreateWithoutProductOrderInput, PhoneItemUncheckedCreateWithoutProductOrderInput>
  }

  export type PhoneItemCreateManyProductOrderInputEnvelope = {
    data: PhoneItemCreateManyProductOrderInput | PhoneItemCreateManyProductOrderInput[]
    skipDuplicates?: boolean
  }

  export type AssignLogCreateWithoutProductOrderInput = {
    phoneNumber: string
    status: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    user: UserCreateNestedOneWithoutAssignLogsInput
  }

  export type AssignLogUncheckedCreateWithoutProductOrderInput = {
    id?: number
    phoneNumber: string
    status: string
    message?: string | null
    userId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AssignLogCreateOrConnectWithoutProductOrderInput = {
    where: AssignLogWhereUniqueInput
    create: XOR<AssignLogCreateWithoutProductOrderInput, AssignLogUncheckedCreateWithoutProductOrderInput>
  }

  export type AssignLogCreateManyProductOrderInputEnvelope = {
    data: AssignLogCreateManyProductOrderInput | AssignLogCreateManyProductOrderInput[]
    skipDuplicates?: boolean
  }

  export type StorageUpsertWithoutProductOrdersInput = {
    update: XOR<StorageUpdateWithoutProductOrdersInput, StorageUncheckedUpdateWithoutProductOrdersInput>
    create: XOR<StorageCreateWithoutProductOrdersInput, StorageUncheckedCreateWithoutProductOrdersInput>
    where?: StorageWhereInput
  }

  export type StorageUpdateToOneWithWhereWithoutProductOrdersInput = {
    where?: StorageWhereInput
    data: XOR<StorageUpdateWithoutProductOrdersInput, StorageUncheckedUpdateWithoutProductOrdersInput>
  }

  export type StorageUpdateWithoutProductOrdersInput = {
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    otp?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    users?: UserUpdateManyWithoutStorageNestedInput
  }

  export type StorageUncheckedUpdateWithoutProductOrdersInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    otp?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    users?: UserUncheckedUpdateManyWithoutStorageNestedInput
  }

  export type PhoneItemUpsertWithWhereUniqueWithoutProductOrderInput = {
    where: PhoneItemWhereUniqueInput
    update: XOR<PhoneItemUpdateWithoutProductOrderInput, PhoneItemUncheckedUpdateWithoutProductOrderInput>
    create: XOR<PhoneItemCreateWithoutProductOrderInput, PhoneItemUncheckedCreateWithoutProductOrderInput>
  }

  export type PhoneItemUpdateWithWhereUniqueWithoutProductOrderInput = {
    where: PhoneItemWhereUniqueInput
    data: XOR<PhoneItemUpdateWithoutProductOrderInput, PhoneItemUncheckedUpdateWithoutProductOrderInput>
  }

  export type PhoneItemUpdateManyWithWhereWithoutProductOrderInput = {
    where: PhoneItemScalarWhereInput
    data: XOR<PhoneItemUpdateManyMutationInput, PhoneItemUncheckedUpdateManyWithoutProductOrderInput>
  }

  export type PhoneItemScalarWhereInput = {
    AND?: PhoneItemScalarWhereInput | PhoneItemScalarWhereInput[]
    OR?: PhoneItemScalarWhereInput[]
    NOT?: PhoneItemScalarWhereInput | PhoneItemScalarWhereInput[]
    id?: StringFilter<"PhoneItem"> | string
    phoneNumber?: StringFilter<"PhoneItem"> | string
    status?: IntFilter<"PhoneItem"> | number
    addedDate?: DateTimeNullableFilter<"PhoneItem"> | Date | string | null
    tariffCode?: StringNullableFilter<"PhoneItem"> | string | null
    productOrderId?: StringFilter<"PhoneItem"> | string
    createdAt?: DateTimeFilter<"PhoneItem"> | Date | string
    updatedAt?: DateTimeFilter<"PhoneItem"> | Date | string
  }

  export type AssignLogUpsertWithWhereUniqueWithoutProductOrderInput = {
    where: AssignLogWhereUniqueInput
    update: XOR<AssignLogUpdateWithoutProductOrderInput, AssignLogUncheckedUpdateWithoutProductOrderInput>
    create: XOR<AssignLogCreateWithoutProductOrderInput, AssignLogUncheckedCreateWithoutProductOrderInput>
  }

  export type AssignLogUpdateWithWhereUniqueWithoutProductOrderInput = {
    where: AssignLogWhereUniqueInput
    data: XOR<AssignLogUpdateWithoutProductOrderInput, AssignLogUncheckedUpdateWithoutProductOrderInput>
  }

  export type AssignLogUpdateManyWithWhereWithoutProductOrderInput = {
    where: AssignLogScalarWhereInput
    data: XOR<AssignLogUpdateManyMutationInput, AssignLogUncheckedUpdateManyWithoutProductOrderInput>
  }

  export type AssignLogScalarWhereInput = {
    AND?: AssignLogScalarWhereInput | AssignLogScalarWhereInput[]
    OR?: AssignLogScalarWhereInput[]
    NOT?: AssignLogScalarWhereInput | AssignLogScalarWhereInput[]
    id?: IntFilter<"AssignLog"> | number
    phoneNumber?: StringFilter<"AssignLog"> | string
    status?: StringFilter<"AssignLog"> | string
    message?: StringNullableFilter<"AssignLog"> | string | null
    productOrderId?: StringFilter<"AssignLog"> | string
    userId?: IntFilter<"AssignLog"> | number
    createdAt?: DateTimeFilter<"AssignLog"> | Date | string
    updatedAt?: DateTimeFilter<"AssignLog"> | Date | string
  }

  export type ProductOrderCreateWithoutPhoneItemsInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
    storage: StorageCreateNestedOneWithoutProductOrdersInput
    assignLogs?: AssignLogCreateNestedManyWithoutProductOrderInput
  }

  export type ProductOrderUncheckedCreateWithoutPhoneItemsInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    storageId: number
    createdAt?: Date | string
    updatedAt?: Date | string
    assignLogs?: AssignLogUncheckedCreateNestedManyWithoutProductOrderInput
  }

  export type ProductOrderCreateOrConnectWithoutPhoneItemsInput = {
    where: ProductOrderWhereUniqueInput
    create: XOR<ProductOrderCreateWithoutPhoneItemsInput, ProductOrderUncheckedCreateWithoutPhoneItemsInput>
  }

  export type ProductOrderUpsertWithoutPhoneItemsInput = {
    update: XOR<ProductOrderUpdateWithoutPhoneItemsInput, ProductOrderUncheckedUpdateWithoutPhoneItemsInput>
    create: XOR<ProductOrderCreateWithoutPhoneItemsInput, ProductOrderUncheckedCreateWithoutPhoneItemsInput>
    where?: ProductOrderWhereInput
  }

  export type ProductOrderUpdateToOneWithWhereWithoutPhoneItemsInput = {
    where?: ProductOrderWhereInput
    data: XOR<ProductOrderUpdateWithoutPhoneItemsInput, ProductOrderUncheckedUpdateWithoutPhoneItemsInput>
  }

  export type ProductOrderUpdateWithoutPhoneItemsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    storage?: StorageUpdateOneRequiredWithoutProductOrdersNestedInput
    assignLogs?: AssignLogUpdateManyWithoutProductOrderNestedInput
  }

  export type ProductOrderUncheckedUpdateWithoutPhoneItemsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    storageId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    assignLogs?: AssignLogUncheckedUpdateManyWithoutProductOrderNestedInput
  }

  export type ProductOrderCreateWithoutAssignLogsInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
    storage: StorageCreateNestedOneWithoutProductOrdersInput
    phoneItems?: PhoneItemCreateNestedManyWithoutProductOrderInput
  }

  export type ProductOrderUncheckedCreateWithoutAssignLogsInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    storageId: number
    createdAt?: Date | string
    updatedAt?: Date | string
    phoneItems?: PhoneItemUncheckedCreateNestedManyWithoutProductOrderInput
  }

  export type ProductOrderCreateOrConnectWithoutAssignLogsInput = {
    where: ProductOrderWhereUniqueInput
    create: XOR<ProductOrderCreateWithoutAssignLogsInput, ProductOrderUncheckedCreateWithoutAssignLogsInput>
  }

  export type UserCreateWithoutAssignLogsInput = {
    phoneNumber: string
    uid?: string | null
    name?: string | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
    storage: StorageCreateNestedOneWithoutUsersInput
  }

  export type UserUncheckedCreateWithoutAssignLogsInput = {
    id?: number
    phoneNumber: string
    uid?: string | null
    name?: string | null
    status?: string
    storageId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserCreateOrConnectWithoutAssignLogsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutAssignLogsInput, UserUncheckedCreateWithoutAssignLogsInput>
  }

  export type ProductOrderUpsertWithoutAssignLogsInput = {
    update: XOR<ProductOrderUpdateWithoutAssignLogsInput, ProductOrderUncheckedUpdateWithoutAssignLogsInput>
    create: XOR<ProductOrderCreateWithoutAssignLogsInput, ProductOrderUncheckedCreateWithoutAssignLogsInput>
    where?: ProductOrderWhereInput
  }

  export type ProductOrderUpdateToOneWithWhereWithoutAssignLogsInput = {
    where?: ProductOrderWhereInput
    data: XOR<ProductOrderUpdateWithoutAssignLogsInput, ProductOrderUncheckedUpdateWithoutAssignLogsInput>
  }

  export type ProductOrderUpdateWithoutAssignLogsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    storage?: StorageUpdateOneRequiredWithoutProductOrdersNestedInput
    phoneItems?: PhoneItemUpdateManyWithoutProductOrderNestedInput
  }

  export type ProductOrderUncheckedUpdateWithoutAssignLogsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    storageId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    phoneItems?: PhoneItemUncheckedUpdateManyWithoutProductOrderNestedInput
  }

  export type UserUpsertWithoutAssignLogsInput = {
    update: XOR<UserUpdateWithoutAssignLogsInput, UserUncheckedUpdateWithoutAssignLogsInput>
    create: XOR<UserCreateWithoutAssignLogsInput, UserUncheckedCreateWithoutAssignLogsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutAssignLogsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutAssignLogsInput, UserUncheckedUpdateWithoutAssignLogsInput>
  }

  export type UserUpdateWithoutAssignLogsInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    storage?: StorageUpdateOneRequiredWithoutUsersNestedInput
  }

  export type UserUncheckedUpdateWithoutAssignLogsInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    storageId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type StorageCreateWithoutUsersInput = {
    username: string
    password: string
    otp: string
    code: string
    status?: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    productOrders?: ProductOrderCreateNestedManyWithoutStorageInput
  }

  export type StorageUncheckedCreateWithoutUsersInput = {
    id?: number
    username: string
    password: string
    otp: string
    code: string
    status?: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    productOrders?: ProductOrderUncheckedCreateNestedManyWithoutStorageInput
  }

  export type StorageCreateOrConnectWithoutUsersInput = {
    where: StorageWhereUniqueInput
    create: XOR<StorageCreateWithoutUsersInput, StorageUncheckedCreateWithoutUsersInput>
  }

  export type AssignLogCreateWithoutUserInput = {
    phoneNumber: string
    status: string
    message?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    productOrder: ProductOrderCreateNestedOneWithoutAssignLogsInput
  }

  export type AssignLogUncheckedCreateWithoutUserInput = {
    id?: number
    phoneNumber: string
    status: string
    message?: string | null
    productOrderId: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AssignLogCreateOrConnectWithoutUserInput = {
    where: AssignLogWhereUniqueInput
    create: XOR<AssignLogCreateWithoutUserInput, AssignLogUncheckedCreateWithoutUserInput>
  }

  export type AssignLogCreateManyUserInputEnvelope = {
    data: AssignLogCreateManyUserInput | AssignLogCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type StorageUpsertWithoutUsersInput = {
    update: XOR<StorageUpdateWithoutUsersInput, StorageUncheckedUpdateWithoutUsersInput>
    create: XOR<StorageCreateWithoutUsersInput, StorageUncheckedCreateWithoutUsersInput>
    where?: StorageWhereInput
  }

  export type StorageUpdateToOneWithWhereWithoutUsersInput = {
    where?: StorageWhereInput
    data: XOR<StorageUpdateWithoutUsersInput, StorageUncheckedUpdateWithoutUsersInput>
  }

  export type StorageUpdateWithoutUsersInput = {
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    otp?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    productOrders?: ProductOrderUpdateManyWithoutStorageNestedInput
  }

  export type StorageUncheckedUpdateWithoutUsersInput = {
    id?: IntFieldUpdateOperationsInput | number
    username?: StringFieldUpdateOperationsInput | string
    password?: StringFieldUpdateOperationsInput | string
    otp?: StringFieldUpdateOperationsInput | string
    code?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    productOrders?: ProductOrderUncheckedUpdateManyWithoutStorageNestedInput
  }

  export type AssignLogUpsertWithWhereUniqueWithoutUserInput = {
    where: AssignLogWhereUniqueInput
    update: XOR<AssignLogUpdateWithoutUserInput, AssignLogUncheckedUpdateWithoutUserInput>
    create: XOR<AssignLogCreateWithoutUserInput, AssignLogUncheckedCreateWithoutUserInput>
  }

  export type AssignLogUpdateWithWhereUniqueWithoutUserInput = {
    where: AssignLogWhereUniqueInput
    data: XOR<AssignLogUpdateWithoutUserInput, AssignLogUncheckedUpdateWithoutUserInput>
  }

  export type AssignLogUpdateManyWithWhereWithoutUserInput = {
    where: AssignLogScalarWhereInput
    data: XOR<AssignLogUpdateManyMutationInput, AssignLogUncheckedUpdateManyWithoutUserInput>
  }

  export type UserCreateManyStorageInput = {
    id?: number
    phoneNumber: string
    uid?: string | null
    name?: string | null
    status?: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type ProductOrderCreateManyStorageInput = {
    id: string
    name: string
    status: number
    tariffId: string
    orderInfoId: string
    tariffName: string
    pckCode: string
    orderDate: Date | string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type UserUpdateWithoutStorageInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    assignLogs?: AssignLogUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutStorageInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    assignLogs?: AssignLogUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateManyWithoutStorageInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    uid?: NullableStringFieldUpdateOperationsInput | string | null
    name?: NullableStringFieldUpdateOperationsInput | string | null
    status?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type ProductOrderUpdateWithoutStorageInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    phoneItems?: PhoneItemUpdateManyWithoutProductOrderNestedInput
    assignLogs?: AssignLogUpdateManyWithoutProductOrderNestedInput
  }

  export type ProductOrderUncheckedUpdateWithoutStorageInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    phoneItems?: PhoneItemUncheckedUpdateManyWithoutProductOrderNestedInput
    assignLogs?: AssignLogUncheckedUpdateManyWithoutProductOrderNestedInput
  }

  export type ProductOrderUncheckedUpdateManyWithoutStorageInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    tariffId?: StringFieldUpdateOperationsInput | string
    orderInfoId?: StringFieldUpdateOperationsInput | string
    tariffName?: StringFieldUpdateOperationsInput | string
    pckCode?: StringFieldUpdateOperationsInput | string
    orderDate?: DateTimeFieldUpdateOperationsInput | Date | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PhoneItemCreateManyProductOrderInput = {
    id: string
    phoneNumber: string
    status: number
    addedDate?: Date | string | null
    tariffCode?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AssignLogCreateManyProductOrderInput = {
    id?: number
    phoneNumber: string
    status: string
    message?: string | null
    userId: number
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type PhoneItemUpdateWithoutProductOrderInput = {
    id?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    addedDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tariffCode?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PhoneItemUncheckedUpdateWithoutProductOrderInput = {
    id?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    addedDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tariffCode?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type PhoneItemUncheckedUpdateManyWithoutProductOrderInput = {
    id?: StringFieldUpdateOperationsInput | string
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    addedDate?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    tariffCode?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignLogUpdateWithoutProductOrderInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    user?: UserUpdateOneRequiredWithoutAssignLogsNestedInput
  }

  export type AssignLogUncheckedUpdateWithoutProductOrderInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    userId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignLogUncheckedUpdateManyWithoutProductOrderInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    userId?: IntFieldUpdateOperationsInput | number
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignLogCreateManyUserInput = {
    id?: number
    phoneNumber: string
    status: string
    message?: string | null
    productOrderId: string
    createdAt?: Date | string
    updatedAt?: Date | string
  }

  export type AssignLogUpdateWithoutUserInput = {
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    productOrder?: ProductOrderUpdateOneRequiredWithoutAssignLogsNestedInput
  }

  export type AssignLogUncheckedUpdateWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    productOrderId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }

  export type AssignLogUncheckedUpdateManyWithoutUserInput = {
    id?: IntFieldUpdateOperationsInput | number
    phoneNumber?: StringFieldUpdateOperationsInput | string
    status?: StringFieldUpdateOperationsInput | string
    message?: NullableStringFieldUpdateOperationsInput | string | null
    productOrderId?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}