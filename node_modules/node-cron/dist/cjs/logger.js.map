{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../src/logger.ts"], "names": [], "mappings": ";;AAEA,MAAM,WAAW,GAA6B;IAC5C,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,UAAU;IAChB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,UAAU;CAClB,CAAC;AAEF,MAAM,KAAK,GAAG,UAAU,CAAC;AACzB,MAAM,KAAK,GAAG,SAAS,CAAC;AAExB,SAAS,GAAG,CAAC,KAAe,EAAE,OAAe,EAAE,KAAW;IACxD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAC3C,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;IACvC,MAAM,MAAM,GAAG,IAAI,SAAS,WAAW,OAAO,CAAC,GAAG,KAAK,KAAK,cAAc,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE,CAAC;IAC7G,MAAM,MAAM,GAAG,GAAG,MAAM,IAAI,OAAO,EAAE,CAAC;IAEtC,QAAQ,KAAK,EAAE,CAAC;QACd,KAAK,OAAO;YACV,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;YACnC,MAAM;QACR,KAAK,OAAO;YACR,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;YACnC,MAAM;QACV,KAAK,MAAM;YACT,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,MAAM;QACR,KAAK,MAAM,CAAC;QACZ;YACE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,MAAM;IACV,CAAC;AACH,CAAC;AAED,MAAM,MAAM,GAAG;IACb,IAAI,CAAC,OAAe;QAClB,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAAC,OAAe;QAClB,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACvB,CAAC;IACD,KAAK,CAAC,OAAuB,EAAE,GAAW;QACxC,IAAI,OAAO,YAAY,KAAK,EAAE,CAAC;YAC7B,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IACD,KAAK,CAAC,OAAuB,EAAE,GAAW;QACxC,IAAI,OAAO,YAAY,KAAK,EAAE,CAAC;YAC7B,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;CACF,CAAC;AAEF,kBAAe,MAAM,CAAC"}