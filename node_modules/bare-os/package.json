{"name": "bare-os", "version": "3.6.1", "description": "Operating system utilities for Javascript", "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package": "./package.json", "./constants": "./lib/constants.js", "./errors": "./lib/errors.js"}, "files": ["index.js", "index.d.ts", "binding.c", "binding.js", "CMakeLists.txt", "lib", "prebuilds"], "addon": true, "scripts": {"test": "prettier . --check && bare test.js"}, "repository": {"type": "git", "url": "git+https://github.com/holepunchto/bare-os.git"}, "author": "Holepunch", "license": "Apache-2.0", "bugs": {"url": "https://github.com/holepunchto/bare-os/issues"}, "homepage": "https://github.com/holepunchto/bare-os#readme", "engines": {"bare": ">=1.14.0"}, "devDependencies": {"brittle": "^3.1.1", "cmake-bare": "^1.1.6", "prettier": "^3.4.2", "prettier-config-standard": "^7.0.0"}}