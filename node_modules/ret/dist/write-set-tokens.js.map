{"version": 3, "file": "write-set-tokens.js", "sourceRoot": "", "sources": ["../lib/write-set-tokens.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,mCAA6D;AAC7D,oDAAsC;AAGtC;;;;GAIG;AACH,SAAgB,OAAO,CAAC,QAAgB;IACtC,OAAO,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAC9B,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YACxB,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBACvB,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;oBACvB,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC;AAND,0BAMC;AAED;;;;;GAKG;AACH,SAAS,SAAS,CAAC,GAAc,EAAE,EAAE,MAAM,EAAE,GAAG,EAAa;IAC3D,uDAAuD;IACvD,yDAAyD;IACzD,IAAI,GAAG,KAAK,GAAG,CAAC,MAAM,EAAE;QACtB,OAAO,KAAK,CAAC;KACd;IACD,MAAM,GAAG,GAAG,MAAM,EAAE,CAAC;IACrB,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;QACtB,IAAI,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,GAAG,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,KAAK,aAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE,CAAC;QAC9E,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE;YACZ,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;SAClB;aAAM;YACL,OAAO,KAAK,CAAC;SACd;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;GAKG;AACH,SAAgB,cAAc,CAAC,GAAQ,EAAE,QAAQ,GAAG,KAAK;IACvD,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;QACjC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;KAChC;IACD,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE;QAClC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;KAChC;IACD,uEAAuE;IACvE,IAAI,GAAG,CAAC,GAAG,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;QAClD,OAAO,GAAG,CAAC;KACZ;IACD,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE;QACvC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;KAChC;IACD,IAAI,WAAW,GAAG,EAAE,CAAC;IACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACvC,MAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC1B,WAAW,IAAI,aAAa,CAAC,MAAM,CAAC,CAAC;KACtC;IACD,MAAM,QAAQ,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,WAAW,EAAE,CAAC;IACvD,OAAO,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,GAAG,CAAC;AAC/C,CAAC;AArBD,wCAqBC;AAED;;;;GAIG;AACH,SAAS,aAAa,CAAC,GAAuB;IAC5C,IAAI,GAAG,CAAC,IAAI,KAAK,aAAK,CAAC,IAAI,EAAE;QAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KAC3B;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,aAAK,CAAC,KAAK,EAAE;QACnC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;KAClD;IACD,OAAO,cAAc,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC"}