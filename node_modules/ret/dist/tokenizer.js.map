{"version": 3, "file": "tokenizer.js", "sourceRoot": "", "sources": ["../lib/tokenizer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA,6CAA+B;AAC/B,mCAAqE;AACrE,6CAA+B;AAI/B;;GAEG;AACH,MAAM,qBAAqB,GAAG,eAAe,CAAC;AAE9C;;GAEG;AACH,MAAM,iBAAiB,GAAG,kBAAkB,CAAC;AAE7C,MAAM,KAAK,GAAG,IAAI,CAAC;AAEnB;;;;;GAKG;AACU,QAAA,SAAS,GAAG,CAAC,SAAiB,EAAQ,EAAE;IACnD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAS,CAAC;IACrB,IAAI,KAAK,GAAS,EAAE,IAAI,EAAE,aAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IAElD,6CAA6C;IAC7C,IAAI,SAAS,GAAiB,KAAK,CAAC;IACpC,IAAI,IAAI,GAAY,KAAK,CAAC,KAAK,CAAC;IAChC,IAAI,UAAU,GAAqB,EAAE,CAAC;IAEtC,IAAI,cAAc,GAAmB,EAAE,CAAC;IACxC,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,EAAE;QAChC,MAAM,IAAI,WAAW,CACnB,gCACE,SACF,kCAAkC,GAAG,GAAG,CAAC,EAAE,CAC5C,CAAC;IACJ,CAAC,CAAC;IAEF,mCAAmC;IACnC,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;IAErC,4CAA4C;IAC5C,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE;QACrB,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;YACpB,iDAAiD;YACjD,KAAK,IAAI;gBACP,IAAI,CAAC,KAAK,GAAG,CAAC,MAAM,EAAE;oBACpB,MAAM,IAAI,WAAW,CACnB,gCACE,SACF,yBAAyB,CAC1B,CAAC;iBACH;gBACD,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;oBACpB,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;wBAChD,MAAM;oBAER,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;wBAChD,MAAM;oBAER,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;wBACxB,MAAM;oBAER,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;wBAC3B,MAAM;oBAER,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;wBACvB,MAAM;oBAER,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;wBAC1B,MAAM;oBAER,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;wBAC7B,MAAM;oBAER,KAAK,GAAG;wBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;wBAChC,MAAM;oBAER;wBACE,yBAAyB;wBACzB,kCAAkC;wBAClC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;4BACjB,IAAI,MAAM,GAAG,CAAC,CAAC;4BAEf,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;gCAC3C,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;6BACpB;4BAED,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;4BACjC,MAAM,SAAS,GAAc,EAAE,IAAI,EAAE,aAAK,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;4BAE9D,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;4BACrB,cAAc,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;4BAE1E,qBAAqB;yBACpB;6BAAM;4BACL,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAK,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;yBACzD;iBACJ;gBAED,MAAM;YAGR,eAAe;YACf,KAAK,GAAG;gBACN,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;gBAChD,MAAM;YAER,KAAK,GAAG;gBACN,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,aAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC;gBAChD,MAAM;YAGR,sBAAsB;YACtB,KAAK,GAAG,CAAC,CAAC;gBACR,6CAA6C;gBAC7C,IAAI,GAAG,CAAC;gBACR,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAClB,GAAG,GAAG,IAAI,CAAC;oBACX,CAAC,EAAE,CAAC;iBACL;qBAAM;oBACL,GAAG,GAAG,KAAK,CAAC;iBACb;gBAED,mCAAmC;gBACnC,IAAI,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;gBAE9D,qCAAqC;gBACrC,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAI,CAAC,IAAI,CAAC;oBACR,IAAI,EAAE,aAAK,CAAC,GAAG;oBACf,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC;oBACnB,GAAG;iBACJ,CAAC,CAAC;gBAEH,MAAM;aACP;YAGD,oCAAoC;YACpC,KAAK,GAAG;gBACN,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC1B,MAAM;YAGR,yBAAyB;YACzB,KAAK,GAAG,CAAC,CAAC;gBACR,gBAAgB;gBAChB,IAAI,KAAK,GAAU;oBACjB,IAAI,EAAE,aAAK,CAAC,KAAK;oBACjB,KAAK,EAAE,EAAE;oBACT,QAAQ,EAAE,IAAI;iBACf,CAAC;gBAEF,sCAAsC;gBACtC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBAClB,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;oBACf,CAAC,IAAI,CAAC,CAAC;oBAEP,wBAAwB;oBACxB,IAAI,CAAC,KAAK,GAAG,EAAE;wBACb,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC;wBACxB,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;wBAEzB,4BAA4B;qBAC3B;yBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;wBACpB,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;wBAC3B,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;qBACxB;yBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;wBACpB,IAAI,IAAI,GAAG,EAAE,CAAC;wBAEd,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;4BACtC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;4BACf,CAAC,EAAE,CAAC;yBACL;6BAAM;4BACL,MAAM,IAAI,WAAW,CACnB,gCACE,SACF,6CAA6C,GAAG,CAAC,CAAC,CAAC,GAAG;gCACtD,wBAAwB,CAAC,GAAG,CAAC,EAAE,CAChC,CAAC;yBACH;wBAED,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,IAAI,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;4BACvD,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC;4BACf,CAAC,EAAE,CAAC;yBACL;wBAED,IAAI,CAAC,IAAI,EAAE;4BACT,MAAM,IAAI,WAAW,CACnB,gCACE,SACF,6CAA6C,GAAG,CAAC,CAAC,CAAC,GAAG;gCACtD,wBAAwB,CAAC,GAAG,CAAC,EAAE,CAChC,CAAC;yBACH;wBAED,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;4BAClB,MAAM,IAAI,WAAW,CACnB,gCACE,SACF,qDAAqD;gCACrD,KAAK,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,EAAE,CAClC,CAAC;yBACH;wBAED,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;wBAClB,CAAC,EAAE,CAAC;qBACL;yBAAM,IAAI,CAAC,KAAK,GAAG,EAAE;wBACpB,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;qBACxB;yBAAM;wBACL,MAAM,IAAI,WAAW,CACnB,gCACE,SACF,gCAAgC,CAAC,GAAG;4BACpC,wBAAwB,CAAC,GAAG,CAAC,EAAE,CAChC,CAAC;qBACH;iBACF;qBAAM;oBACL,UAAU,IAAI,CAAC,CAAC;iBACjB;gBAED,4CAA4C;gBAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAEjB,wDAAwD;gBACxD,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAE3B,yCAAyC;gBACzC,SAAS,GAAG,KAAK,CAAC;gBAClB,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;gBAEnB,MAAM;aACP;YAGD,0BAA0B;YAC1B,KAAK,GAAG;gBACN,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC3B,MAAM,IAAI,WAAW,CACnB,gCACE,SACF,4BAA4B,CAAC,GAAG,CAAC,EAAE,CACpC,CAAC;iBACH;gBACD,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC;gBAE7B,kCAAkC;gBAClC,sCAAsC;gBACtC,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC;oBACxB,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;oBACjD,SAAS,CAAC,KAAK,CAAC;gBAElB,MAAM;YAGR,2CAA2C;YAC3C,KAAK,GAAG,CAAC,CAAC;gBACR,2DAA2D;gBAC3D,kBAAkB;gBAClB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;oBACtB,SAAS,CAAC,OAAO,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;oBACtC,OAAO,SAAS,CAAC,KAAK,CAAC;iBACxB;gBACD,4DAA4D;gBAC5D,IAAI,KAAK,GAAY,EAAE,CAAC;gBACxB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC9B,IAAI,GAAG,KAAK,CAAC;gBAEb,MAAM;aACP;YAGD,cAAc;YACd,4DAA4D;YAC5D,mCAAmC;YACnC,yDAAyD;YACzD,qDAAqD;YACrD,KAAK,GAAG,CAAC,CAAC;gBACR,IAAI,EAAE,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC;gBAC3D,IAAI,EAAE,KAAK,IAAI,EAAE;oBACf,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrB,SAAS,CAAC,CAAC,CAAC,CAAC;qBACd;oBACD,GAAG,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC1B,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC;oBAC3D,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;oBAElB,IAAI,CAAC,IAAI,CAAC;wBACR,IAAI,EAAE,aAAK,CAAC,UAAU;wBACtB,GAAG;wBACH,GAAG;wBACH,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE;qBAClB,CAAC,CAAC;iBACJ;qBAAM;oBACL,IAAI,CAAC,IAAI,CAAC;wBACR,IAAI,EAAE,aAAK,CAAC,IAAI;wBAChB,KAAK,EAAE,GAAG;qBACX,CAAC,CAAC;iBACJ;gBAED,MAAM;aACP;YAED,KAAK,GAAG;gBACN,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,SAAS,CAAC,CAAC,CAAC,CAAC;iBACd;gBACD,IAAI,CAAC,IAAI,CAAC;oBACR,IAAI,EAAE,aAAK,CAAC,UAAU;oBACtB,GAAG,EAAE,CAAC;oBACN,GAAG,EAAE,CAAC;oBACN,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE;iBAClB,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,GAAG;gBACN,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,SAAS,CAAC,CAAC,CAAC,CAAC;iBACd;gBACD,IAAI,CAAC,IAAI,CAAC;oBACR,IAAI,EAAE,aAAK,CAAC,UAAU;oBACtB,GAAG,EAAE,CAAC;oBACN,GAAG,EAAE,QAAQ;oBACb,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE;iBAClB,CAAC,CAAC;gBAEH,MAAM;YAER,KAAK,GAAG;gBACN,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACrB,SAAS,CAAC,CAAC,CAAC,CAAC;iBACd;gBACD,IAAI,CAAC,IAAI,CAAC;oBACR,IAAI,EAAE,aAAK,CAAC,UAAU;oBACtB,GAAG,EAAE,CAAC;oBACN,GAAG,EAAE,QAAQ;oBACb,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE;iBAClB,CAAC,CAAC;gBAEH,MAAM;YAGR,qDAAqD;YACrD;gBACE,IAAI,CAAC,IAAI,CAAC;oBACR,IAAI,EAAE,aAAK,CAAC,IAAI;oBAChB,KAAK,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;iBACvB,CAAC,CAAC;SACN;KACF;IAED,4CAA4C;IAC5C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAI,WAAW,CACnB,gCACE,SACF,uBAAuB,CACxB,CAAC;KACH;IAED,gBAAgB,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;IAE7C,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,SAAS,gBAAgB,CAAC,cAA8B,EAAE,UAAkB;IAC1E,oDAAoD;IACpD,sDAAsD;IACtD,+BAA+B;IAC/B,KAAK,MAAM,IAAI,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE;QAC3C,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;YACrC,oEAAoE;YACpE,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,aAAK,CAAC,IAAI,CAAC;YAEjC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YACpD,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;YAChD,oEAAoE;YACpE,iEAAiE;YACjE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE;gBACjC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAEV,OAAO,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBACvD,CAAC,IAAI,CAAC,CAAC;iBACR;gBAED,IAAI,CAAC,KAAK,CAAC,EAAE;oBACX,uDAAuD;oBACvD,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;oBACjD,CAAC,IAAI,CAAC,CAAC;iBACR;qBAAM;oBACL,6DAA6D;oBAC7D,gEAAgE;oBAChE,oEAAoE;oBACpE,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBAC7D;gBAED,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;oBAE/C,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;wBACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;4BACd,IAAI,EAAE,aAAK,CAAC,IAAI;4BAChB,KAAK,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;yBAC1B,CAAC,CAAC;qBACJ;oBACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;iBAC1B;aACF;SACF;KACF;AACH,CAAC"}