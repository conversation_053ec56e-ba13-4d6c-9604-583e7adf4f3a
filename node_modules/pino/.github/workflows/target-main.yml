name: PR Target Check

on:
  pull_request_target:
    types: [opened]

permissions:
  pull-requests: write

jobs:
  comment:
    if: ${{ github.base_ref != "master" }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '⚠️ This pull request does not target the master branch.'
            })
