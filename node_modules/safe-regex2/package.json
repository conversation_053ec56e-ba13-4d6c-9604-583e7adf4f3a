{"name": "safe-regex2", "version": "5.0.0", "description": "detect possibly catastrophic, exponential-time regular expressions", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "dependencies": {"ret": "~0.5.0"}, "devDependencies": {"@fastify/pre-commit": "^2.1.0", "c8": "^10.1.3", "eslint": "^9.17.0", "neostandard": "^0.12.0", "tape": "^5.7.5", "tsd": "^0.31.0"}, "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "c8 tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/fastify/safe-regex2.git"}, "bugs": {"url": "https://github.com/fastify/safe-regex2/issues"}, "homepage": "https://github.com/fastify/safe-regex2", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "keywords": ["catastrophic", "exponential", "regex", "safe", "sandbox"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT"}