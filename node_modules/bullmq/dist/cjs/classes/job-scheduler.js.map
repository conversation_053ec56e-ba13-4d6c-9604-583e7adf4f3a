{"version": 3, "file": "job-scheduler.js", "sourceRoot": "", "sources": ["../../../src/classes/job-scheduler.ts"], "names": [], "mappings": ";;;;AAAA,6CAA8C;AAa9C,+BAA4B;AAC5B,6CAAyC;AAEzC,oCAAyD;AACzD,oCAAqC;AAErC,MAAa,YAAa,SAAQ,sBAAS;IAGzC,YACE,IAAY,EACZ,IAAuB,EACvB,UAAmC;QAEnC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAE9B,IAAI,CAAC,cAAc;YACjB,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,6BAAqB,CAAC;IAC7E,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,cAAsB,EACtB,UAAqD,EACrD,OAAU,EACV,OAAU,EACV,IAAiC,EACjC,EAAE,QAAQ,EAAE,UAAU,EAA8C;QAEpE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC;QAErD,IAAI,OAAO,IAAI,KAAK,EAAE;YACpB,MAAM,IAAI,KAAK,CACb,sEAAsE,CACvE,CAAC;SACH;QAED,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,EAAE;YACtB,MAAM,IAAI,KAAK,CACb,2EAA2E,CAC5E,CAAC;SACH;QAED,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,SAAS,EAAE;YAClD,MAAM,IAAI,KAAK,CACb,8EAA8E,CAC/E,CAAC;SACH;QAED,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,KAAK,EAAE;YAC9C,OAAO,CAAC,IAAI,CACV,0GAA0G,CAC3G,CAAC;SACH;QAED,mEAAmE;QACnE,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,IACE,OAAO,UAAU,CAAC,KAAK,KAAK,WAAW;YACvC,cAAc,GAAG,UAAU,CAAC,KAAK,EACjC;YACA,OAAO;SACR;QAED,yDAAyD;QACzD,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACrB,MAAM,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC;QAC/B,IAAI,OAAO,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,OAAQ,CAAC,CAAC,OAAO,EAAE,EAAE;YACjD,OAAO;SACR;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC;QACxC,GAAG,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,CAAC;QAE1C,uDAAuD;QACvD,MAAM,EAAE,SAAS,EAAE,WAAW,KAA4B,UAAU,EAAjC,kBAAkB,kBAAK,UAAU,EAA9D,4BAAiD,CAAa,CAAC;QACrE,IAAI,WAAW,GAAG,GAAG,CAAC;QACtB,IAAI,SAAS,EAAE;YACb,WAAW,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE,CAAC;YAC5C,WAAW,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;SACrD;QAED,IAAI,UAAkB,CAAC;QACvB,IAAI,SAAS,GAAG,MAAM,IAAI,CAAC,CAAC;QAE5B,IAAI,KAAK,EAAE;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC;YACzD,MAAM,QAAQ,GAAG,QAAQ,GAAG,KAAK,CAAC;YAClC,IAAI,UAAU,IAAI,MAAM,EAAE;gBACxB,UAAU,GAAG,QAAQ,CAAC;aACvB;iBAAM;gBACL,UAAU,GAAG,QAAQ,CAAC;gBACtB,SAAS,GAAG,WAAW,GAAG,QAAQ,CAAC;gBAEnC,uEAAuE;gBACvE,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;aAC3C;SACF;aAAM,IAAI,OAAO,EAAE;YAClB,UAAU,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;YAEjE,IAAI,UAAU,GAAG,GAAG,EAAE;gBACpB,UAAU,GAAG,GAAG,CAAC;aAClB;SACF;QAED,IAAI,UAAU,EAAE;YACd,OAAO,IAAI,CAAC,KAAK,CACf,gBAAQ,CAAC,QAAQ,EACjB,KAAK,EACL,GAAG,IAAI,CAAC,IAAI,IAAI,OAAO,EAAE,EACzB,KAAK,EAAE,IAAI,EAAE,sBAAsB,EAAE,EAAE;;gBACrC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;gBAE/B,IAAI,sBAAsB,EAAE;oBAC1B,MAAM,WAAW,GAAG,MAAA,IAAI,CAAC,SAAS,0CAAE,WAAW,CAAC;oBAChD,MAAM,iBAAiB,GACrB,CAAA,MAAA,IAAI,CAAC,SAAS,0CAAE,QAAQ;wBACxB,CAAC,CAAC,WAAW,IAAI,sBAAsB,CAAC,CAAC;oBAE3C,IAAI,iBAAiB,IAAI,WAAW,EAAE;wBACpC,SAAS,GAAG;4BACV,QAAQ,EAAE,iBAAiB;4BAC3B,WAAW;yBACZ,CAAC;qBACH;iBACF;gBAED,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CACpC,UAAU,EACV,cAAc,kCAET,IAAI,KACP,MAAM,EAAE,kBAAkB,EAC1B,SAAS,KAEX,cAAc,EACd,SAAS,CACV,CAAC;gBAEF,IAAI,QAAQ,EAAE;oBACZ,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAC9C,cAAc,EACd,UAAU,EACV,IAAI,CAAC,SAAS,CAAC,OAAO,OAAO,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAC7D,SAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EACpB;wBACE,IAAI,EAAE,OAAO;wBACb,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,SAAS;wBAC1D,EAAE,EAAE,UAAU,CAAC,EAAE;wBACjB,OAAO;wBACP,KAAK;wBACL,KAAK;qBACN,EACD,SAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAC1B,UAAU,CACX,CAAC;oBAEF,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,CACtB,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,KAAK,CACN,CAAC;oBAEF,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC;oBAEf,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC;wBAClB,CAAC,2BAAmB,CAAC,cAAc,CAAC,EAAE,cAAc;wBACpD,CAAC,2BAAmB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE;qBACpC,CAAC,CAAC;oBAEH,OAAO,GAAG,CAAC;iBACZ;qBAAM;oBACL,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,4BAA4B,CAC3D,cAAc,EACd,UAAU,EACV,IAAI,CAAC,SAAS,CAAC,OAAO,OAAO,KAAK,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAC7D,SAAG,CAAC,UAAU,CAAC,UAAU,CAAC,EAC1B,UAAU,CACX,CAAC;oBAEF,IAAI,KAAK,EAAE;wBACT,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,CACtB,IAAI,EACJ,OAAO,EACP,OAAO,EACP,UAAU,EACV,KAAK,CACN,CAAC;wBAEF,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC;wBAEf,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,aAAa,CAAC;4BAClB,CAAC,2BAAmB,CAAC,cAAc,CAAC,EAAE,cAAc;4BACpD,CAAC,2BAAmB,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE;yBACpC,CAAC,CAAC;wBAEH,OAAO,GAAG,CAAC;qBACZ;iBACF;YACH,CAAC,CACF,CAAC;SACH;IACH,CAAC;IAEO,cAAc,CACpB,UAAkB,EAClB,cAAsB,EACtB,IAAiB,EACjB,YAAoB,EACpB,MAAe;;QAEf,EAAE;QACF,6CAA6C;QAC7C,EAAE;QACF,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACvC,cAAc;YACd,UAAU;SACX,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,KAAK,GAAG,UAAU,GAAG,MAAM,GAAG,GAAG,CAAC;QAExC,MAAM,UAAU,mCACX,IAAI,KACP,KAAK,EACL,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAC5B,SAAS,EAAE,GAAG,EACd,UAAU,EAAE,UAAU,EACtB,YAAY,EAAE,cAAc,GAC7B,CAAC;QAEF,UAAU,CAAC,MAAM,mCACZ,IAAI,CAAC,MAAM,KACd,KAAK,EAAE,YAAY,EACnB,MAAM,EACN,OAAO,EAAE,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,OAAO;gBAC3B,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE;gBACzC,CAAC,CAAC,SAAS,GACd,CAAC;QAEF,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,aAAa,CACnB,MAAmB,EACnB,IAAO,EACP,UAAkB,EAClB,MAAc,EACd,cAAsB,EACtB,IAAiB,EACjB,IAAO,EACP,YAAoB;IACpB,0DAA0D;IAC1D,UAAmB;QAEnB,EAAE;QACF,6CAA6C;QAC7C,EAAE;QACF,MAAM,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACvC,cAAc;YACd,UAAU;SACX,CAAC,CAAC;QAEH,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,KAAK,GAAG,UAAU,GAAG,MAAM,GAAG,GAAG,CAAC;QAExC,MAAM,UAAU,mCACX,IAAI,KACP,KAAK,EACL,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAC5B,SAAS,EAAE,GAAG,EACd,UAAU,EAAE,UAAU,EACtB,YAAY,EAAE,cAAc,GAC7B,CAAC;QAEF,UAAU,CAAC,MAAM,mCAAQ,IAAI,CAAC,MAAM,KAAE,KAAK,EAAE,YAAY,GAAE,CAAC;QAE5D,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,CAAU,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;QACvE,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEnB,IAAI,UAAU,EAAE;YACd,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;SAC9C;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,cAAsB;QAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,MAAmB,EACnB,GAAW,EACX,IAAa;QAEb,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC,sBAAsB,CAAI,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAEO,sBAAsB,CAC5B,GAAW,EACX,OAAY,EACZ,IAAa;QAEb,IAAI,OAAO,EAAE;YACX,MAAM,gBAAgB,GAAwB;gBAC5C,GAAG;gBACH,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI;aACL,CAAC;YAEF,IAAI,OAAO,CAAC,EAAE,EAAE;gBACd,gBAAgB,CAAC,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;aACxD;YAED,IAAI,OAAO,CAAC,KAAK,EAAE;gBACjB,gBAAgB,CAAC,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;aAClD;YAED,IAAI,OAAO,CAAC,OAAO,EAAE;gBACnB,gBAAgB,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aACtD;YAED,IAAI,OAAO,CAAC,EAAE,EAAE;gBACd,gBAAgB,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;aAClC;YAED,IAAI,OAAO,CAAC,OAAO,EAAE;gBACnB,gBAAgB,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;aAC5C;YAED,IAAI,OAAO,CAAC,KAAK,EAAE;gBACjB,gBAAgB,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;aACxC;YAED,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE;gBAChC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAClD,OAAO,CAAC,IAAI,EACZ,OAAO,CAAC,IAAI,CACb,CAAC;aACH;YAED,OAAO,gBAAgB,CAAC;SACzB;QAED,kFAAkF;QAClF,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACrB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAClC;IACH,CAAC;IAEO,SAAS,CAAC,GAAW,EAAE,IAAa;QAC1C,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;QAEhD,OAAO;YACL,GAAG;YACH,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;YACb,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;YACnB,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;YAClC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;YACnB,OAAO;YACP,IAAI;SACL,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,EAAU;QAEV,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC;QAElE,OAAO,IAAI,CAAC,sBAAsB,CAChC,EAAE,EACF,UAAU,CAAC,CAAC,CAAC,IAAA,iBAAS,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EACzC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAC7B,CAAC;IACJ,CAAC;IAEO,mBAAmB,CACzB,OAAgB,EAChB,OAAgB;QAEhB,MAAM,QAAQ,GAAgC,EAAE,CAAC;QACjD,IAAI,OAAO,EAAE;YACX,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;SACrC;QACD,IAAI,OAAO,EAAE;YACX,QAAQ,CAAC,IAAI,GAAG,SAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,KAAK,GAAG,CAAC,EACT,GAAG,GAAG,CAAC,CAAC,EACR,GAAG,GAAG,KAAK;QAEX,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC;QACjC,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAE1C,MAAM,MAAM,GAAG,GAAG;YAChB,CAAC,CAAC,MAAM,MAAM,CAAC,MAAM,CAAC,gBAAgB,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,CAAC;YACjE,CAAC,CAAC,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,KAAK,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;QAEvE,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACzC,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,gBAAgB,CAAI,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrE,CAAC;SACH;QACD,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QAC1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC;QAEjC,OAAO,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACxC,CAAC;IAEO,qBAAqB,CAAC,EAC5B,UAAU,EACV,cAAc,GAIf;QACC,OAAO,UAAU,cAAc,IAAI,UAAU,EAAE,CAAC;IAClD,CAAC;CACF;AA3aD,oCA2aC;AAEM,MAAM,qBAAqB,GAAG,CACnC,MAAc,EACd,IAAmB,EACC,EAAE;IACtB,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IAEzB,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;IACrC,MAAM,QAAQ,GAAG,IAAA,6BAAe,EAAC,OAAO,kCACnC,IAAI,KACP,WAAW,IACX,CAAC;IAEH,IAAI;QACF,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;SAC7B;aAAM;YACL,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;SAClC;KACF;IAAC,OAAO,CAAC,EAAE;QACV,eAAe;KAChB;AACH,CAAC,CAAC;AArBW,QAAA,qBAAqB,yBAqBhC"}