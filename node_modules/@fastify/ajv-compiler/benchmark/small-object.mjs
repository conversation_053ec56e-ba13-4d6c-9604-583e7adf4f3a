import cronometro from 'cronometro'

import fjs from 'fast-json-stringify'
import AjvCompiler from '../index.js'

const fjsSerialize = buildFJSSerializerFunction({
  type: 'object',
  properties: {
    hello: { type: 'string' },
    name: { type: 'string' }
  }
})
const ajvSerialize = buildAJVSerializerFunction({
  properties: {
    hello: { type: 'string' },
    name: { type: 'string' }
  }
})

await cronometro({
  'fast-json-stringify': function () {
    fjsSerialize({ hello: '<PERSON>ia<PERSON>', name: '<PERSON>' })
  },
  'ajv serializer': function () {
    ajvSerialize({ hello: '<PERSON><PERSON><PERSON>', name: '<PERSON>' })
  }
})

function buildFJSSerializerFunction (schema) {
  return fjs(schema)
}

function buildAJVSerializerFunction (schema) {
  const factory = AjvCompiler({ jtdSerializer: true })
  const compiler = factory({}, { customOptions: {} })
  return compiler({ schema })
}
