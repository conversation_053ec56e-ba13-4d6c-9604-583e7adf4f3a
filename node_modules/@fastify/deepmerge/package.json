{"name": "@fastify/deepmerge", "version": "2.0.2", "description": "Merges the enumerable properties of two or more objects deeply.", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"lint": "eslint", "lint:fix": "eslint --fix", "test": "npm run test:unit && npm run test:typescript", "test:unit": "c8 tape test/*.js", "test:typescript": "tsd"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/deepmerge.git"}, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/deepmerge/issues"}, "homepage": "https://github.com/fastify/deepmerge#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"eslint": "^9.17.0", "neostandard": "^0.12.0", "tape": "^5.7.5", "c8": "^10.1.3", "tsd": "^0.31.1"}, "files": ["LICENSE", "README.md", "index.js", "types/index.d.ts"], "keywords": ["merge", "deep", "recursive", "object", "immutable"]}