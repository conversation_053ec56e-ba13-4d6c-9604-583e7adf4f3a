{"version": 3, "file": "fileUtil.js", "sourceRoot": "", "sources": ["../../src/fileUtil.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAC,SAAS,EAAE,KAAK,EAAC,MAAM,oBAAoB,CAAC;AACpD,OAAO,EAAC,gBAAgB,EAAC,MAAM,SAAS,CAAC;AACzC,OAAO,EAAC,KAAK,EAAE,OAAO,EAAC,MAAM,kBAAkB,CAAC;AAChD,OAAO,KAAK,IAAI,MAAM,WAAW,CAAC;AAElC,OAAO,EAAC,MAAM,EAAC,MAAM,aAAa,CAAC;AAEnC,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,MAAM,aAAa,GAAG,KAAK,CAAC,6BAA6B,CAAC,CAAC;AAE3D;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,WAAmB,EACnB,UAAkB;IAElB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QACjC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,CAAC,CAAC;IACvD,CAAC;IACD,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACjC,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,CAAC;QAC/C,MAAM,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,EAAC,GAAG,EAAE,UAAU,EAAC,CAAC,CAAC;IAC3D,CAAC;SAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;QAC5C,MAAM,UAAU,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;IACrD,CAAC;SAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,MAAM,KAAK,CAAC,UAAU,CAAC,CAAC;QACxB,MAAM,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;IAC5C,CAAC;SAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QACxC,sBAAsB;QACtB,MAAM,MAAM,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC,eAAe,UAAU,EAAE,CAAC,EAAE;YACnE,GAAG,EAAE;gBACH,cAAc,EAAE,cAAc;aAC/B;SACF,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CACb,qBAAqB,WAAW,OAAO,UAAU,KAAK,MAAM,CAAC,MAAM,EAAE,CACtE,CAAC;QACJ,CAAC;IACH,CAAC;SAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QAC3C,MAAM,UAAU,CAAC,WAAW,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;IAClD,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,KAAK,CAAC,+BAA+B,WAAW,EAAE,CAAC,CAAC;IAChE,CAAC;AACH,CAAC;AAED,SAAS,qBAAqB,CAC5B,KAAoD;IAEpD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC;QAClC,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ;YACjC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC;gBACxC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,QAAQ,EAAE,CAAC;YACb,CAAC;QACH,CAAC;QAED,KAAK,CAAC,QAAQ;YACZ,IAAI,KAAK,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;gBAC3B,QAAQ,EAAE,CAAC;YACb,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;gBAClB,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACrC,CAAC;QACH,CAAC;KACF,CAAC,CAAC;IAEH,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE;QAC1B,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACtC,wDAAwD;YACxD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,KAAK,CAAC,MAAM;SACT,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;QACjB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC,CAAC;SACD,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE;QACf,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEL,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE;QACvB,OAAO,MAAM,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,2BAA2B,GAAG;IACzC,EAAE,EAAE,IAAI;IACR,KAAK,EAAE,OAAO;CACf,CAAC;AAEF;;GAEG;AACH,KAAK,UAAU,UAAU,CACvB,OAAe,EACf,UAAkB,EAClB,qBAA+D;IAE/D,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;IACrC,OAAO,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACjD,SAAS,WAAW,CAAC,WAAmB;YACtC,OAAO,CAAC,KAAY,EAAE,EAAE;gBACtB,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC/C,KAAK,GAAG,IAAI,KAAK,CACf,KAAK,WAAW,+CAA+C,EAC/D;wBACE,KAAK,EAAE,KAAK;qBACb,CACF,CAAC;gBACJ,CAAC;gBACD,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC;QACJ,CAAC;QACD,MAAM,MAAM,GAAG,KAAK,CAClB,2BAA2B,CAAC,qBAAqB,CAAC,EAClD,CAAC,IAAI,CAAC,EACN;YACE,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC;SACnC,CACF;aACE,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,qBAAqB,CAAC,CAAC;aACjD,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;YACnB,aAAa,CAAC,GAAG,qBAAqB,iBAAiB,IAAI,EAAE,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEL,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACtC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;QACtC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAC5B,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1E,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,UAAU,CAAC,OAAe,EAAE,UAAkB;IAC3D,MAAM,EAAC,MAAM,EAAC,GAAG,SAAS,CAAC,SAAS,EAAE;QACpC,QAAQ;QACR,WAAW;QACX,aAAa;QACb,OAAO;KACR,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAClE,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,EAAE,CAAC,CAAC;IAC7D,CAAC;IACD,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAE,CAAC;IAE9B,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACpC,OAAO,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;QACrD,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,SAAU,EAAE,OAAO,CAAC,CAAC;QAEnD,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC;IACnD,CAAC;YAAS,CAAC;QACT,SAAS,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC;IACxD,CAAC;AACH,CAAC"}