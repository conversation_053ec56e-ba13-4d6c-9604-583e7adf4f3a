{"name": "find-my-way", "version": "9.3.0", "description": "Crazy fast http radix based router", "main": "index.js", "type": "commonjs", "types": "index.d.ts", "scripts": {"bench": "node ./benchmark/bench.js", "bench:cmp": "node ./benchmark/compare-branches.js", "bench:cmp:ci": "node ./benchmark/compare-branches.js --ci", "test:lint": "standard", "test:typescript": "tsd", "test": "standard && borp && npm run test:typescript"}, "repository": {"type": "git", "url": "git+https://github.com/delvedor/find-my-way.git"}, "keywords": ["http", "router", "radix", "fast", "speed"], "engines": {"node": ">=20"}, "author": "<PERSON> - @delvedor (http://delved.org)", "license": "MIT", "bugs": {"url": "https://github.com/delvedor/find-my-way/issues"}, "homepage": "https://github.com/delvedor/find-my-way#readme", "devDependencies": {"@types/node": "^22.10.2", "benchmark": "^2.1.4", "borp": "^0.19.0", "chalk": "^5.4.1", "inquirer": "^12.3.0", "pre-commit": "^1.2.2", "proxyquire": "^2.1.3", "rfdc": "^1.3.0", "simple-git": "^3.7.1", "standard": "^17.0.0", "tsd": "^0.31.0"}, "dependencies": {"fast-deep-equal": "^3.1.3", "fast-querystring": "^1.0.0", "safe-regex2": "^5.0.0"}, "tsd": {"directory": "test/types"}}