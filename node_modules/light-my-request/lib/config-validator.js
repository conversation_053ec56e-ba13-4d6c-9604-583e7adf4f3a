// This file is autogenerated by build/build-validation.js, do not edit
/* c8 ignore start */
/* eslint-disable */
"use strict";
module.exports = validate10;
module.exports.default = validate10;
const schema11 = {"type":"object","properties":{"url":{"oneOf":[{"type":"string"},{"type":"object","properties":{"protocol":{"type":"string"},"hostname":{"type":"string"},"pathname":{"type":"string"}},"additionalProperties":true,"required":["pathname"]}]},"path":{"oneOf":[{"type":"string"},{"type":"object","properties":{"protocol":{"type":"string"},"hostname":{"type":"string"},"pathname":{"type":"string"}},"additionalProperties":true,"required":["pathname"]}]},"cookies":{"type":"object","additionalProperties":true},"headers":{"type":"object","additionalProperties":true},"query":{"anyOf":[{"type":"object","additionalProperties":true},{"type":"string"}]},"simulate":{"type":"object","properties":{"end":{"type":"boolean"},"split":{"type":"boolean"},"error":{"type":"boolean"},"close":{"type":"boolean"}}},"authority":{"type":"string"},"remoteAddress":{"type":"string"},"method":{"type":"string","enum":["ACL","BIND","CHECKOUT","CONNECT","COPY","DELETE","GET","HEAD","LINK","LOCK","M-SEARCH","MERGE","MKACTIVITY","MKCALENDAR","MKCOL","MOVE","NOTIFY","OPTIONS","PATCH","POST","PROPFIND","PROPPATCH","PURGE","PUT","QUERY","REBIND","REPORT","SEARCH","SOURCE","SUBSCRIBE","TRACE","UNBIND","UNLINK","UNLOCK","UNSUBSCRIBE","acl","bind","checkout","connect","copy","delete","get","head","link","lock","m-search","merge","mkactivity","mkcalendar","mkcol","move","notify","options","patch","post","propfind","proppatch","purge","put","query","rebind","report","search","source","subscribe","trace","unbind","unlink","unlock","unsubscribe"]},"validate":{"type":"boolean"}},"additionalProperties":true,"oneOf":[{"required":["url"]},{"required":["path"]}]};

function validate10(data, {instancePath="", parentData, parentDataProperty, rootData=data}={}){
let vErrors = null;
let errors = 0;
const _errs1 = errors;
let valid0 = false;
let passing0 = null;
const _errs2 = errors;
if(data && typeof data == "object" && !Array.isArray(data)){
let missing0;
if((data.url === undefined) && (missing0 = "url")){
const err0 = {instancePath,schemaPath:"#/oneOf/0/required",keyword:"required",params:{missingProperty: missing0},message:"must have required property '"+missing0+"'"};
if(vErrors === null){
vErrors = [err0];
}
else {
vErrors.push(err0);
}
errors++;
}
}
var _valid0 = _errs2 === errors;
if(_valid0){
valid0 = true;
passing0 = 0;
}
const _errs3 = errors;
if(data && typeof data == "object" && !Array.isArray(data)){
let missing1;
if((data.path === undefined) && (missing1 = "path")){
const err1 = {instancePath,schemaPath:"#/oneOf/1/required",keyword:"required",params:{missingProperty: missing1},message:"must have required property '"+missing1+"'"};
if(vErrors === null){
vErrors = [err1];
}
else {
vErrors.push(err1);
}
errors++;
}
}
var _valid0 = _errs3 === errors;
if(_valid0 && valid0){
valid0 = false;
passing0 = [passing0, 1];
}
else {
if(_valid0){
valid0 = true;
passing0 = 1;
}
}
if(!valid0){
const err2 = {instancePath,schemaPath:"#/oneOf",keyword:"oneOf",params:{passingSchemas: passing0},message:"must match exactly one schema in oneOf"};
if(vErrors === null){
vErrors = [err2];
}
else {
vErrors.push(err2);
}
errors++;
validate10.errors = vErrors;
return false;
}
else {
errors = _errs1;
if(vErrors !== null){
if(_errs1){
vErrors.length = _errs1;
}
else {
vErrors = null;
}
}
}
if(errors === 0){
if(data && typeof data == "object" && !Array.isArray(data)){
if(data.url !== undefined){
let data0 = data.url;
const _errs5 = errors;
const _errs6 = errors;
let valid2 = false;
let passing1 = null;
const _errs7 = errors;
if(typeof data0 !== "string"){
let dataType0 = typeof data0;
let coerced0 = undefined;
if(!(coerced0 !== undefined)){
if(dataType0 == "number" || dataType0 == "boolean"){
coerced0 = "" + data0;
}
else if(data0 === null){
coerced0 = "";
}
else {
const err3 = {instancePath:instancePath+"/url",schemaPath:"#/properties/url/oneOf/0/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err3];
}
else {
vErrors.push(err3);
}
errors++;
}
}
if(coerced0 !== undefined){
data0 = coerced0;
if(data !== undefined){
data["url"] = coerced0;
}
}
}
var _valid1 = _errs7 === errors;
if(_valid1){
valid2 = true;
passing1 = 0;
}
const _errs9 = errors;
if(errors === _errs9){
if(data0 && typeof data0 == "object" && !Array.isArray(data0)){
let missing2;
if((data0.pathname === undefined) && (missing2 = "pathname")){
const err4 = {instancePath:instancePath+"/url",schemaPath:"#/properties/url/oneOf/1/required",keyword:"required",params:{missingProperty: missing2},message:"must have required property '"+missing2+"'"};
if(vErrors === null){
vErrors = [err4];
}
else {
vErrors.push(err4);
}
errors++;
}
else {
if(data0.protocol !== undefined){
let data1 = data0.protocol;
const _errs12 = errors;
if(typeof data1 !== "string"){
let dataType1 = typeof data1;
let coerced1 = undefined;
if(!(coerced1 !== undefined)){
if(dataType1 == "number" || dataType1 == "boolean"){
coerced1 = "" + data1;
}
else if(data1 === null){
coerced1 = "";
}
else {
const err5 = {instancePath:instancePath+"/url/protocol",schemaPath:"#/properties/url/oneOf/1/properties/protocol/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err5];
}
else {
vErrors.push(err5);
}
errors++;
}
}
if(coerced1 !== undefined){
data1 = coerced1;
if(data0 !== undefined){
data0["protocol"] = coerced1;
}
}
}
var valid3 = _errs12 === errors;
}
else {
var valid3 = true;
}
if(valid3){
if(data0.hostname !== undefined){
let data2 = data0.hostname;
const _errs14 = errors;
if(typeof data2 !== "string"){
let dataType2 = typeof data2;
let coerced2 = undefined;
if(!(coerced2 !== undefined)){
if(dataType2 == "number" || dataType2 == "boolean"){
coerced2 = "" + data2;
}
else if(data2 === null){
coerced2 = "";
}
else {
const err6 = {instancePath:instancePath+"/url/hostname",schemaPath:"#/properties/url/oneOf/1/properties/hostname/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err6];
}
else {
vErrors.push(err6);
}
errors++;
}
}
if(coerced2 !== undefined){
data2 = coerced2;
if(data0 !== undefined){
data0["hostname"] = coerced2;
}
}
}
var valid3 = _errs14 === errors;
}
else {
var valid3 = true;
}
if(valid3){
if(data0.pathname !== undefined){
let data3 = data0.pathname;
const _errs16 = errors;
if(typeof data3 !== "string"){
let dataType3 = typeof data3;
let coerced3 = undefined;
if(!(coerced3 !== undefined)){
if(dataType3 == "number" || dataType3 == "boolean"){
coerced3 = "" + data3;
}
else if(data3 === null){
coerced3 = "";
}
else {
const err7 = {instancePath:instancePath+"/url/pathname",schemaPath:"#/properties/url/oneOf/1/properties/pathname/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err7];
}
else {
vErrors.push(err7);
}
errors++;
}
}
if(coerced3 !== undefined){
data3 = coerced3;
if(data0 !== undefined){
data0["pathname"] = coerced3;
}
}
}
var valid3 = _errs16 === errors;
}
else {
var valid3 = true;
}
}
}
}
}
else {
const err8 = {instancePath:instancePath+"/url",schemaPath:"#/properties/url/oneOf/1/type",keyword:"type",params:{type: "object"},message:"must be object"};
if(vErrors === null){
vErrors = [err8];
}
else {
vErrors.push(err8);
}
errors++;
}
}
var _valid1 = _errs9 === errors;
if(_valid1 && valid2){
valid2 = false;
passing1 = [passing1, 1];
}
else {
if(_valid1){
valid2 = true;
passing1 = 1;
}
}
if(!valid2){
const err9 = {instancePath:instancePath+"/url",schemaPath:"#/properties/url/oneOf",keyword:"oneOf",params:{passingSchemas: passing1},message:"must match exactly one schema in oneOf"};
if(vErrors === null){
vErrors = [err9];
}
else {
vErrors.push(err9);
}
errors++;
validate10.errors = vErrors;
return false;
}
else {
errors = _errs6;
if(vErrors !== null){
if(_errs6){
vErrors.length = _errs6;
}
else {
vErrors = null;
}
}
}
var valid1 = _errs5 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.path !== undefined){
let data4 = data.path;
const _errs18 = errors;
const _errs19 = errors;
let valid4 = false;
let passing2 = null;
const _errs20 = errors;
if(typeof data4 !== "string"){
let dataType4 = typeof data4;
let coerced4 = undefined;
if(!(coerced4 !== undefined)){
if(dataType4 == "number" || dataType4 == "boolean"){
coerced4 = "" + data4;
}
else if(data4 === null){
coerced4 = "";
}
else {
const err10 = {instancePath:instancePath+"/path",schemaPath:"#/properties/path/oneOf/0/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err10];
}
else {
vErrors.push(err10);
}
errors++;
}
}
if(coerced4 !== undefined){
data4 = coerced4;
if(data !== undefined){
data["path"] = coerced4;
}
}
}
var _valid2 = _errs20 === errors;
if(_valid2){
valid4 = true;
passing2 = 0;
}
const _errs22 = errors;
if(errors === _errs22){
if(data4 && typeof data4 == "object" && !Array.isArray(data4)){
let missing3;
if((data4.pathname === undefined) && (missing3 = "pathname")){
const err11 = {instancePath:instancePath+"/path",schemaPath:"#/properties/path/oneOf/1/required",keyword:"required",params:{missingProperty: missing3},message:"must have required property '"+missing3+"'"};
if(vErrors === null){
vErrors = [err11];
}
else {
vErrors.push(err11);
}
errors++;
}
else {
if(data4.protocol !== undefined){
let data5 = data4.protocol;
const _errs25 = errors;
if(typeof data5 !== "string"){
let dataType5 = typeof data5;
let coerced5 = undefined;
if(!(coerced5 !== undefined)){
if(dataType5 == "number" || dataType5 == "boolean"){
coerced5 = "" + data5;
}
else if(data5 === null){
coerced5 = "";
}
else {
const err12 = {instancePath:instancePath+"/path/protocol",schemaPath:"#/properties/path/oneOf/1/properties/protocol/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err12];
}
else {
vErrors.push(err12);
}
errors++;
}
}
if(coerced5 !== undefined){
data5 = coerced5;
if(data4 !== undefined){
data4["protocol"] = coerced5;
}
}
}
var valid5 = _errs25 === errors;
}
else {
var valid5 = true;
}
if(valid5){
if(data4.hostname !== undefined){
let data6 = data4.hostname;
const _errs27 = errors;
if(typeof data6 !== "string"){
let dataType6 = typeof data6;
let coerced6 = undefined;
if(!(coerced6 !== undefined)){
if(dataType6 == "number" || dataType6 == "boolean"){
coerced6 = "" + data6;
}
else if(data6 === null){
coerced6 = "";
}
else {
const err13 = {instancePath:instancePath+"/path/hostname",schemaPath:"#/properties/path/oneOf/1/properties/hostname/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err13];
}
else {
vErrors.push(err13);
}
errors++;
}
}
if(coerced6 !== undefined){
data6 = coerced6;
if(data4 !== undefined){
data4["hostname"] = coerced6;
}
}
}
var valid5 = _errs27 === errors;
}
else {
var valid5 = true;
}
if(valid5){
if(data4.pathname !== undefined){
let data7 = data4.pathname;
const _errs29 = errors;
if(typeof data7 !== "string"){
let dataType7 = typeof data7;
let coerced7 = undefined;
if(!(coerced7 !== undefined)){
if(dataType7 == "number" || dataType7 == "boolean"){
coerced7 = "" + data7;
}
else if(data7 === null){
coerced7 = "";
}
else {
const err14 = {instancePath:instancePath+"/path/pathname",schemaPath:"#/properties/path/oneOf/1/properties/pathname/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err14];
}
else {
vErrors.push(err14);
}
errors++;
}
}
if(coerced7 !== undefined){
data7 = coerced7;
if(data4 !== undefined){
data4["pathname"] = coerced7;
}
}
}
var valid5 = _errs29 === errors;
}
else {
var valid5 = true;
}
}
}
}
}
else {
const err15 = {instancePath:instancePath+"/path",schemaPath:"#/properties/path/oneOf/1/type",keyword:"type",params:{type: "object"},message:"must be object"};
if(vErrors === null){
vErrors = [err15];
}
else {
vErrors.push(err15);
}
errors++;
}
}
var _valid2 = _errs22 === errors;
if(_valid2 && valid4){
valid4 = false;
passing2 = [passing2, 1];
}
else {
if(_valid2){
valid4 = true;
passing2 = 1;
}
}
if(!valid4){
const err16 = {instancePath:instancePath+"/path",schemaPath:"#/properties/path/oneOf",keyword:"oneOf",params:{passingSchemas: passing2},message:"must match exactly one schema in oneOf"};
if(vErrors === null){
vErrors = [err16];
}
else {
vErrors.push(err16);
}
errors++;
validate10.errors = vErrors;
return false;
}
else {
errors = _errs19;
if(vErrors !== null){
if(_errs19){
vErrors.length = _errs19;
}
else {
vErrors = null;
}
}
}
var valid1 = _errs18 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.cookies !== undefined){
let data8 = data.cookies;
const _errs31 = errors;
if(errors === _errs31){
if(!(data8 && typeof data8 == "object" && !Array.isArray(data8))){
validate10.errors = [{instancePath:instancePath+"/cookies",schemaPath:"#/properties/cookies/type",keyword:"type",params:{type: "object"},message:"must be object"}];
return false;
}
}
var valid1 = _errs31 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.headers !== undefined){
let data9 = data.headers;
const _errs34 = errors;
if(errors === _errs34){
if(!(data9 && typeof data9 == "object" && !Array.isArray(data9))){
validate10.errors = [{instancePath:instancePath+"/headers",schemaPath:"#/properties/headers/type",keyword:"type",params:{type: "object"},message:"must be object"}];
return false;
}
}
var valid1 = _errs34 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.query !== undefined){
let data10 = data.query;
const _errs37 = errors;
const _errs38 = errors;
let valid6 = false;
const _errs39 = errors;
if(errors === _errs39){
if(!(data10 && typeof data10 == "object" && !Array.isArray(data10))){
const err17 = {instancePath:instancePath+"/query",schemaPath:"#/properties/query/anyOf/0/type",keyword:"type",params:{type: "object"},message:"must be object"};
if(vErrors === null){
vErrors = [err17];
}
else {
vErrors.push(err17);
}
errors++;
}
}
var _valid3 = _errs39 === errors;
valid6 = valid6 || _valid3;
if(!valid6){
const _errs42 = errors;
if(typeof data10 !== "string"){
let dataType8 = typeof data10;
let coerced8 = undefined;
if(!(coerced8 !== undefined)){
if(dataType8 == "number" || dataType8 == "boolean"){
coerced8 = "" + data10;
}
else if(data10 === null){
coerced8 = "";
}
else {
const err18 = {instancePath:instancePath+"/query",schemaPath:"#/properties/query/anyOf/1/type",keyword:"type",params:{type: "string"},message:"must be string"};
if(vErrors === null){
vErrors = [err18];
}
else {
vErrors.push(err18);
}
errors++;
}
}
if(coerced8 !== undefined){
data10 = coerced8;
if(data !== undefined){
data["query"] = coerced8;
}
}
}
var _valid3 = _errs42 === errors;
valid6 = valid6 || _valid3;
}
if(!valid6){
const err19 = {instancePath:instancePath+"/query",schemaPath:"#/properties/query/anyOf",keyword:"anyOf",params:{},message:"must match a schema in anyOf"};
if(vErrors === null){
vErrors = [err19];
}
else {
vErrors.push(err19);
}
errors++;
validate10.errors = vErrors;
return false;
}
else {
errors = _errs38;
if(vErrors !== null){
if(_errs38){
vErrors.length = _errs38;
}
else {
vErrors = null;
}
}
}
var valid1 = _errs37 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.simulate !== undefined){
let data11 = data.simulate;
const _errs44 = errors;
if(errors === _errs44){
if(data11 && typeof data11 == "object" && !Array.isArray(data11)){
if(data11.end !== undefined){
let data12 = data11.end;
const _errs46 = errors;
if(typeof data12 !== "boolean"){
let coerced9 = undefined;
if(!(coerced9 !== undefined)){
if(data12 === "false" || data12 === 0 || data12 === null){
coerced9 = false;
}
else if(data12 === "true" || data12 === 1){
coerced9 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/simulate/end",schemaPath:"#/properties/simulate/properties/end/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced9 !== undefined){
data12 = coerced9;
if(data11 !== undefined){
data11["end"] = coerced9;
}
}
}
var valid7 = _errs46 === errors;
}
else {
var valid7 = true;
}
if(valid7){
if(data11.split !== undefined){
let data13 = data11.split;
const _errs48 = errors;
if(typeof data13 !== "boolean"){
let coerced10 = undefined;
if(!(coerced10 !== undefined)){
if(data13 === "false" || data13 === 0 || data13 === null){
coerced10 = false;
}
else if(data13 === "true" || data13 === 1){
coerced10 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/simulate/split",schemaPath:"#/properties/simulate/properties/split/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced10 !== undefined){
data13 = coerced10;
if(data11 !== undefined){
data11["split"] = coerced10;
}
}
}
var valid7 = _errs48 === errors;
}
else {
var valid7 = true;
}
if(valid7){
if(data11.error !== undefined){
let data14 = data11.error;
const _errs50 = errors;
if(typeof data14 !== "boolean"){
let coerced11 = undefined;
if(!(coerced11 !== undefined)){
if(data14 === "false" || data14 === 0 || data14 === null){
coerced11 = false;
}
else if(data14 === "true" || data14 === 1){
coerced11 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/simulate/error",schemaPath:"#/properties/simulate/properties/error/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced11 !== undefined){
data14 = coerced11;
if(data11 !== undefined){
data11["error"] = coerced11;
}
}
}
var valid7 = _errs50 === errors;
}
else {
var valid7 = true;
}
if(valid7){
if(data11.close !== undefined){
let data15 = data11.close;
const _errs52 = errors;
if(typeof data15 !== "boolean"){
let coerced12 = undefined;
if(!(coerced12 !== undefined)){
if(data15 === "false" || data15 === 0 || data15 === null){
coerced12 = false;
}
else if(data15 === "true" || data15 === 1){
coerced12 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/simulate/close",schemaPath:"#/properties/simulate/properties/close/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced12 !== undefined){
data15 = coerced12;
if(data11 !== undefined){
data11["close"] = coerced12;
}
}
}
var valid7 = _errs52 === errors;
}
else {
var valid7 = true;
}
}
}
}
}
else {
validate10.errors = [{instancePath:instancePath+"/simulate",schemaPath:"#/properties/simulate/type",keyword:"type",params:{type: "object"},message:"must be object"}];
return false;
}
}
var valid1 = _errs44 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.authority !== undefined){
let data16 = data.authority;
const _errs54 = errors;
if(typeof data16 !== "string"){
let dataType13 = typeof data16;
let coerced13 = undefined;
if(!(coerced13 !== undefined)){
if(dataType13 == "number" || dataType13 == "boolean"){
coerced13 = "" + data16;
}
else if(data16 === null){
coerced13 = "";
}
else {
validate10.errors = [{instancePath:instancePath+"/authority",schemaPath:"#/properties/authority/type",keyword:"type",params:{type: "string"},message:"must be string"}];
return false;
}
}
if(coerced13 !== undefined){
data16 = coerced13;
if(data !== undefined){
data["authority"] = coerced13;
}
}
}
var valid1 = _errs54 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.remoteAddress !== undefined){
let data17 = data.remoteAddress;
const _errs56 = errors;
if(typeof data17 !== "string"){
let dataType14 = typeof data17;
let coerced14 = undefined;
if(!(coerced14 !== undefined)){
if(dataType14 == "number" || dataType14 == "boolean"){
coerced14 = "" + data17;
}
else if(data17 === null){
coerced14 = "";
}
else {
validate10.errors = [{instancePath:instancePath+"/remoteAddress",schemaPath:"#/properties/remoteAddress/type",keyword:"type",params:{type: "string"},message:"must be string"}];
return false;
}
}
if(coerced14 !== undefined){
data17 = coerced14;
if(data !== undefined){
data["remoteAddress"] = coerced14;
}
}
}
var valid1 = _errs56 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.method !== undefined){
let data18 = data.method;
const _errs58 = errors;
if(typeof data18 !== "string"){
let dataType15 = typeof data18;
let coerced15 = undefined;
if(!(coerced15 !== undefined)){
if(dataType15 == "number" || dataType15 == "boolean"){
coerced15 = "" + data18;
}
else if(data18 === null){
coerced15 = "";
}
else {
validate10.errors = [{instancePath:instancePath+"/method",schemaPath:"#/properties/method/type",keyword:"type",params:{type: "string"},message:"must be string"}];
return false;
}
}
if(coerced15 !== undefined){
data18 = coerced15;
if(data !== undefined){
data["method"] = coerced15;
}
}
}
if(!((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((((data18 === "ACL") || (data18 === "BIND")) || (data18 === "CHECKOUT")) || (data18 === "CONNECT")) || (data18 === "COPY")) || (data18 === "DELETE")) || (data18 === "GET")) || (data18 === "HEAD")) || (data18 === "LINK")) || (data18 === "LOCK")) || (data18 === "M-SEARCH")) || (data18 === "MERGE")) || (data18 === "MKACTIVITY")) || (data18 === "MKCALENDAR")) || (data18 === "MKCOL")) || (data18 === "MOVE")) || (data18 === "NOTIFY")) || (data18 === "OPTIONS")) || (data18 === "PATCH")) || (data18 === "POST")) || (data18 === "PROPFIND")) || (data18 === "PROPPATCH")) || (data18 === "PURGE")) || (data18 === "PUT")) || (data18 === "QUERY")) || (data18 === "REBIND")) || (data18 === "REPORT")) || (data18 === "SEARCH")) || (data18 === "SOURCE")) || (data18 === "SUBSCRIBE")) || (data18 === "TRACE")) || (data18 === "UNBIND")) || (data18 === "UNLINK")) || (data18 === "UNLOCK")) || (data18 === "UNSUBSCRIBE")) || (data18 === "acl")) || (data18 === "bind")) || (data18 === "checkout")) || (data18 === "connect")) || (data18 === "copy")) || (data18 === "delete")) || (data18 === "get")) || (data18 === "head")) || (data18 === "link")) || (data18 === "lock")) || (data18 === "m-search")) || (data18 === "merge")) || (data18 === "mkactivity")) || (data18 === "mkcalendar")) || (data18 === "mkcol")) || (data18 === "move")) || (data18 === "notify")) || (data18 === "options")) || (data18 === "patch")) || (data18 === "post")) || (data18 === "propfind")) || (data18 === "proppatch")) || (data18 === "purge")) || (data18 === "put")) || (data18 === "query")) || (data18 === "rebind")) || (data18 === "report")) || (data18 === "search")) || (data18 === "source")) || (data18 === "subscribe")) || (data18 === "trace")) || (data18 === "unbind")) || (data18 === "unlink")) || (data18 === "unlock")) || (data18 === "unsubscribe"))){
validate10.errors = [{instancePath:instancePath+"/method",schemaPath:"#/properties/method/enum",keyword:"enum",params:{allowedValues: schema11.properties.method.enum},message:"must be equal to one of the allowed values"}];
return false;
}
var valid1 = _errs58 === errors;
}
else {
var valid1 = true;
}
if(valid1){
if(data.validate !== undefined){
let data19 = data.validate;
const _errs60 = errors;
if(typeof data19 !== "boolean"){
let coerced16 = undefined;
if(!(coerced16 !== undefined)){
if(data19 === "false" || data19 === 0 || data19 === null){
coerced16 = false;
}
else if(data19 === "true" || data19 === 1){
coerced16 = true;
}
else {
validate10.errors = [{instancePath:instancePath+"/validate",schemaPath:"#/properties/validate/type",keyword:"type",params:{type: "boolean"},message:"must be boolean"}];
return false;
}
}
if(coerced16 !== undefined){
data19 = coerced16;
if(data !== undefined){
data["validate"] = coerced16;
}
}
}
var valid1 = _errs60 === errors;
}
else {
var valid1 = true;
}
}
}
}
}
}
}
}
}
}
}
else {
validate10.errors = [{instancePath,schemaPath:"#/type",keyword:"type",params:{type: "object"},message:"must be object"}];
return false;
}
}
validate10.errors = vErrors;
return errors === 0;
}

